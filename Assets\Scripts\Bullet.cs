using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Bullet : MonoBehaviour
{
    [<PERSON><PERSON>("Bullet Settings")]
    public float speed = 10f;
    public float damage = 2f;
    
    [Header("Visual")]
    public Color bulletColor = Color.white;
    
    private Transform target;
    private SpriteRenderer spriteRenderer;
    
    void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
        if (spriteRenderer == null)
        {
            spriteRenderer = gameObject.AddComponent<SpriteRenderer>();
        }
        
        // Create a simple dot sprite
        CreateDotSprite();
    }
    
    void Update()
    {
        if (target == null)
        {
            Destroy(gameObject);
            return;
        }
        
        Vector3 direction = target.position - transform.position;
        float distanceThisFrame = speed * Time.deltaTime;
        
        if (direction.magnitude <= distanceThisFrame)
        {
            HitTarget();
            return;
        }
        
        transform.Translate(direction.normalized * distanceThisFrame, Space.World);
    }
    
    public void Seek(Transform _target)
    {
        target = _target;
    }
    
    public void SetDamage(float _damage)
    {
        damage = _damage;
    }
    
    private void HitTarget()
    {
        Enemy enemy = target.GetComponent<Enemy>();
        if (enemy != null)
        {
            enemy.TakeDamage(damage);
        }
        
        Destroy(gameObject);
    }
    
    private void CreateDotSprite()
    {
        // Create a simple 1x1 white texture
        Texture2D texture = new Texture2D(1, 1);
        texture.SetPixel(0, 0, Color.white);
        texture.Apply();
        
        // Create sprite from texture
        Sprite sprite = Sprite.Create(texture, new Rect(0, 0, 1, 1), new Vector2(0.5f, 0.5f), 1f);
        spriteRenderer.sprite = sprite;
        spriteRenderer.color = bulletColor;
        
        // Set size
        transform.localScale = new Vector3(0.1f, 0.1f, 1f);
    }
}
