using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class GameManager : MonoBehaviour
{
    [Header("Game Settings")]
    public int startingCurrency = 50;
    public int baseMaxHealth = 10;
    public int totalWaves = 5;
    
    [Header("Wave Settings")]
    public float timeBetweenWaves = 10f;
    public List<int> enemiesPerWave = new List<int> { 3, 5, 8, 12, 15 };
    public List<float> enemySpeedPerWave = new List<float> { 2f, 2f, 2.5f, 2.5f, 3f };
    
    [Header("References")]
    public TMP_Text currencyText;
    public TMP_Text waveText;
    public TMP_Text baseHealthText;
    public GameObject enemyPrefab;
    public Transform spawnPoint;
    public List<Transform> waypoints;
    
    [Header("Debug")]
    public bool debugMode = true;
    public GameObject debugPanel;
    
    // Game state
    private int currentCurrency;
    private int currentBaseHealth;
    private int currentWave = 0;
    private bool waveInProgress = false;
    private int enemiesRemaining = 0;
    
    public static GameManager Instance { get; private set; }
    
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        currentCurrency = startingCurrency;
        currentBaseHealth = baseMaxHealth;
        UpdateUI();
        
        if (debugMode && debugPanel != null)
        {
            debugPanel.SetActive(true);
        }
        
        StartCoroutine(StartNextWave());
    }
    
    public void AddCurrency(int amount)
    {
        currentCurrency += amount;
        UpdateUI();
    }
    
    public bool SpendCurrency(int amount)
    {
        if (currentCurrency >= amount)
        {
            currentCurrency -= amount;
            UpdateUI();
            return true;
        }
        return false;
    }
    
    public int GetCurrency()
    {
        return currentCurrency;
    }
    
    public void DamageBase(int damage = 1)
    {
        currentBaseHealth -= damage;
        UpdateUI();
        
        if (currentBaseHealth <= 0)
        {
            GameOver();
        }
    }
    
    public int GetCurrentWave()
    {
        return currentWave;
    }
    
    public bool CanPlaceTower()
    {
        return currentCurrency >= 20;
    }
    
    private IEnumerator StartNextWave()
    {
        if (currentWave >= totalWaves)
        {
            GameWin();
            yield break;
        }
        
        currentWave++;
        UpdateUI();
        
        yield return new WaitForSeconds(timeBetweenWaves);
        
        StartWave();
    }
    
    private void StartWave()
    {
        waveInProgress = true;
        enemiesRemaining = enemiesPerWave[currentWave - 1];
        
        StartCoroutine(SpawnEnemies());
    }
    
    private IEnumerator SpawnEnemies()
    {
        for (int i = 0; i < enemiesPerWave[currentWave - 1]; i++)
        {
            GameObject enemy = Instantiate(enemyPrefab, spawnPoint.position, Quaternion.identity);
            Enemy enemyScript = enemy.GetComponent<Enemy>();
            enemyScript.SetSpeed(enemySpeedPerWave[currentWave - 1]);
            enemyScript.SetWaveManager(this);
            
            yield return new WaitForSeconds(1f);
        }
    }
    
    public void EnemyDefeated()
    {
        enemiesRemaining--;
        AddCurrency(10);
        
        if (enemiesRemaining <= 0)
        {
            waveInProgress = false;
            StartCoroutine(StartNextWave());
        }
    }
    
    private void UpdateUI()
    {
        if (currencyText != null)
            currencyText.text = "Coins: " + currentCurrency;
        
        if (waveText != null)
            waveText.text = "Wave: " + currentWave + "/" + totalWaves;
        
        if (baseHealthText != null)
            baseHealthText.text = "Base HP: " + currentBaseHealth + "/" + baseMaxHealth;
    }
    
    private void GameOver()
    {
        Debug.Log("Game Over!");
        // TODO: Show game over screen
    }
    
    private void GameWin()
    {
        Debug.Log("You Win!");
        // TODO: Show win screen
    }
    
    // Debug methods
    public void Debug_SkipWave()
    {
        if (debugMode)
        {
            StopAllCoroutines();
            StartCoroutine(StartNextWave());
        }
    }
    
    public void Debug_AddCoins()
    {
        if (debugMode)
        {
            AddCurrency(50);
        }
    }
    
    public void Debug_InstantWin()
    {
        if (debugMode)
        {
            GameWin();
        }
    }
    
    public void Debug_AddHealth()
    {
        if (debugMode)
        {
            currentBaseHealth = Mathf.Min(currentBaseHealth + 5, baseMaxHealth);
            UpdateUI();
        }
    }
}
