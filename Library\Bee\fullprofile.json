{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 22624, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 22624, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 22624, "tid": 387224, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 22624, "tid": 387224, "ts": 1753768230056580, "dur": 8, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 22624, "tid": 387224, "ts": 1753768230056598, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 22624, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 22624, "tid": 1, "ts": 1753768229392437, "dur": 4526, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22624, "tid": 1, "ts": 1753768229396969, "dur": 137265, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22624, "tid": 1, "ts": 1753768229534237, "dur": 183658, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 22624, "tid": 387224, "ts": 1753768230056604, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 22624, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229391998, "dur": 7365, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229399365, "dur": 656551, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229399372, "dur": 95, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229399471, "dur": 346, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229399822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229399824, "dur": 297, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229400125, "dur": 323, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229400450, "dur": 2998, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403454, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403458, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403498, "dur": 1, "ph": "X", "name": "ProcessMessages 1340", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403500, "dur": 131, "ph": "X", "name": "ReadAsync 1340", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403636, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403701, "dur": 2, "ph": "X", "name": "ProcessMessages 1444", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403703, "dur": 34, "ph": "X", "name": "ReadAsync 1444", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403742, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403805, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403807, "dur": 52, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403863, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403865, "dur": 39, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403907, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403909, "dur": 50, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403962, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229403964, "dur": 45, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404012, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404015, "dur": 95, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404112, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404137, "dur": 70, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404210, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404212, "dur": 37, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404251, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404254, "dur": 59, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404318, "dur": 98, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404419, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404421, "dur": 75, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404499, "dur": 1, "ph": "X", "name": "ProcessMessages 1276", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404502, "dur": 100, "ph": "X", "name": "ReadAsync 1276", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404604, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404607, "dur": 94, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404704, "dur": 1, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404706, "dur": 74, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404783, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404786, "dur": 101, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404890, "dur": 1, "ph": "X", "name": "ProcessMessages 776", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229404892, "dur": 148, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405043, "dur": 2, "ph": "X", "name": "ProcessMessages 1950", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405046, "dur": 102, "ph": "X", "name": "ReadAsync 1950", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405151, "dur": 1, "ph": "X", "name": "ProcessMessages 1285", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405154, "dur": 95, "ph": "X", "name": "ReadAsync 1285", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405252, "dur": 1, "ph": "X", "name": "ProcessMessages 1020", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405254, "dur": 99, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405356, "dur": 1, "ph": "X", "name": "ProcessMessages 1394", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405358, "dur": 73, "ph": "X", "name": "ReadAsync 1394", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405435, "dur": 1, "ph": "X", "name": "ProcessMessages 1147", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405437, "dur": 71, "ph": "X", "name": "ReadAsync 1147", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405511, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405514, "dur": 71, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405588, "dur": 1, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405590, "dur": 69, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405662, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405664, "dur": 44, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405711, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405713, "dur": 41, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405757, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405759, "dur": 77, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405839, "dur": 1, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405841, "dur": 49, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405892, "dur": 1, "ph": "X", "name": "ProcessMessages 1026", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405895, "dur": 42, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405940, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405942, "dur": 42, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405987, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229405988, "dur": 42, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406034, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406036, "dur": 41, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406080, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406082, "dur": 44, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406129, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406131, "dur": 41, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406175, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406177, "dur": 42, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406223, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406225, "dur": 41, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406268, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406270, "dur": 43, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406317, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406319, "dur": 85, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406406, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406409, "dur": 43, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406455, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406457, "dur": 40, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406500, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406502, "dur": 44, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406549, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406551, "dur": 40, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406593, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406595, "dur": 46, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406644, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406646, "dur": 43, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406692, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406694, "dur": 41, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406738, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406740, "dur": 43, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406786, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406788, "dur": 40, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406832, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406833, "dur": 87, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406924, "dur": 1, "ph": "X", "name": "ProcessMessages 1184", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406926, "dur": 40, "ph": "X", "name": "ReadAsync 1184", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406969, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229406971, "dur": 42, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407016, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407018, "dur": 43, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407064, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407066, "dur": 40, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407110, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407112, "dur": 43, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407158, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407160, "dur": 36, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407199, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407201, "dur": 44, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407249, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407295, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407297, "dur": 41, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407341, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407343, "dur": 89, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407435, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407438, "dur": 40, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407481, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407483, "dur": 38, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407524, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407526, "dur": 39, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407568, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407569, "dur": 43, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407616, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407618, "dur": 52, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407673, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407675, "dur": 43, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407721, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407723, "dur": 41, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407767, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407769, "dur": 38, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407810, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407812, "dur": 90, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407906, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407993, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229407995, "dur": 43, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408041, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408043, "dur": 41, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408086, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408088, "dur": 40, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408131, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408133, "dur": 40, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408178, "dur": 40, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408221, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408223, "dur": 123, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408431, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408434, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408540, "dur": 2, "ph": "X", "name": "ProcessMessages 2115", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408543, "dur": 85, "ph": "X", "name": "ReadAsync 2115", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408639, "dur": 2, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408642, "dur": 48, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408693, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408695, "dur": 143, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408907, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229408910, "dur": 279, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229409336, "dur": 2, "ph": "X", "name": "ProcessMessages 1599", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229409340, "dur": 82, "ph": "X", "name": "ReadAsync 1599", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229409425, "dur": 4, "ph": "X", "name": "ProcessMessages 4366", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229409431, "dur": 44, "ph": "X", "name": "ReadAsync 4366", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229409480, "dur": 44, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229410248, "dur": 2, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229410251, "dur": 326, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229410634, "dur": 282, "ph": "X", "name": "ProcessMessages 6949", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229410981, "dur": 801, "ph": "X", "name": "ReadAsync 6949", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229411944, "dur": 6, "ph": "X", "name": "ProcessMessages 6910", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229411952, "dur": 690, "ph": "X", "name": "ReadAsync 6910", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229413012, "dur": 214, "ph": "X", "name": "ProcessMessages 8860", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229413433, "dur": 768, "ph": "X", "name": "ReadAsync 8860", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229414422, "dur": 71, "ph": "X", "name": "ProcessMessages 10796", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229414778, "dur": 1444, "ph": "X", "name": "ReadAsync 10796", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229416318, "dur": 10, "ph": "X", "name": "ProcessMessages 9877", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229416416, "dur": 968, "ph": "X", "name": "ReadAsync 9877", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229417614, "dur": 74, "ph": "X", "name": "ProcessMessages 15107", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229417788, "dur": 610, "ph": "X", "name": "ReadAsync 15107", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229418432, "dur": 61, "ph": "X", "name": "ProcessMessages 12547", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229418536, "dur": 356, "ph": "X", "name": "ReadAsync 12547", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229418896, "dur": 3, "ph": "X", "name": "ProcessMessages 3265", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229418985, "dur": 871, "ph": "X", "name": "ReadAsync 3265", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229419999, "dur": 60, "ph": "X", "name": "ProcessMessages 3462", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229421393, "dur": 730, "ph": "X", "name": "ReadAsync 3462", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229422130, "dur": 17, "ph": "X", "name": "ProcessMessages 20520", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229422225, "dur": 865, "ph": "X", "name": "ReadAsync 20520", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229423094, "dur": 9, "ph": "X", "name": "ProcessMessages 12022", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229423337, "dur": 353, "ph": "X", "name": "ReadAsync 12022", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229423693, "dur": 114, "ph": "X", "name": "ProcessMessages 3989", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229423901, "dur": 608, "ph": "X", "name": "ReadAsync 3989", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229424631, "dur": 61, "ph": "X", "name": "ProcessMessages 4176", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229424806, "dur": 533, "ph": "X", "name": "ReadAsync 4176", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229425449, "dur": 105, "ph": "X", "name": "ProcessMessages 4305", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229425777, "dur": 605, "ph": "X", "name": "ReadAsync 4305", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229426456, "dur": 4, "ph": "X", "name": "ProcessMessages 4390", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229426636, "dur": 688, "ph": "X", "name": "ReadAsync 4390", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229427421, "dur": 156, "ph": "X", "name": "ProcessMessages 4283", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229427638, "dur": 439, "ph": "X", "name": "ReadAsync 4283", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229428175, "dur": 59, "ph": "X", "name": "ProcessMessages 5594", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229428333, "dur": 383, "ph": "X", "name": "ReadAsync 5594", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229429077, "dur": 258, "ph": "X", "name": "ProcessMessages 3609", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229430399, "dur": 2013, "ph": "X", "name": "ReadAsync 3609", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229432730, "dur": 341, "ph": "X", "name": "ProcessMessages 13223", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229433358, "dur": 615, "ph": "X", "name": "ReadAsync 13223", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229434131, "dur": 66, "ph": "X", "name": "ProcessMessages 11814", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229434922, "dur": 523, "ph": "X", "name": "ReadAsync 11814", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229435652, "dur": 7, "ph": "X", "name": "ProcessMessages 6176", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229435776, "dur": 541, "ph": "X", "name": "ReadAsync 6176", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229436405, "dur": 4, "ph": "X", "name": "ProcessMessages 3897", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229436561, "dur": 639, "ph": "X", "name": "ReadAsync 3897", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229437379, "dur": 5, "ph": "X", "name": "ProcessMessages 4152", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229437461, "dur": 476, "ph": "X", "name": "ReadAsync 4152", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229438260, "dur": 93, "ph": "X", "name": "ProcessMessages 4551", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229438453, "dur": 359, "ph": "X", "name": "ReadAsync 4551", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229438913, "dur": 56, "ph": "X", "name": "ProcessMessages 3134", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229439085, "dur": 129, "ph": "X", "name": "ReadAsync 3134", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229439303, "dur": 79, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229439392, "dur": 49, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229439445, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229439448, "dur": 45, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229439505, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229439507, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229439582, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229439812, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229440091, "dur": 435, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229440648, "dur": 65, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229440768, "dur": 300, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229441231, "dur": 48, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229441312, "dur": 368, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229441766, "dur": 47, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229441896, "dur": 371, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229442270, "dur": 4, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229442276, "dur": 344, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229442667, "dur": 3, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229442673, "dur": 215, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229442939, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229442993, "dur": 211, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229443254, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229443310, "dur": 335, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229443701, "dur": 4, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229443762, "dur": 197, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229444016, "dur": 3, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229444070, "dur": 198, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229444317, "dur": 47, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229444593, "dur": 133, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229444861, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229444905, "dur": 290, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229445353, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229445359, "dur": 107, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229445511, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229445514, "dur": 3979, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229449626, "dur": 46, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229449761, "dur": 325, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229450089, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229450091, "dur": 3248, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229453472, "dur": 40, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229453596, "dur": 494, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229454140, "dur": 95, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229454290, "dur": 10904, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229465332, "dur": 33, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229465399, "dur": 146, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229465581, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229465625, "dur": 1211, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229466995, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229467037, "dur": 317, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229467587, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229467642, "dur": 319, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229468019, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229468071, "dur": 393, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229468467, "dur": 40, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229468509, "dur": 106, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229468663, "dur": 154, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229468864, "dur": 50, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229468997, "dur": 301, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229469342, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229469344, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229469540, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229469583, "dur": 203, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229469812, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229469911, "dur": 298, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229470213, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229470215, "dur": 306, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229470572, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229470625, "dur": 194, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229470863, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229470910, "dur": 236, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229471192, "dur": 49, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229471339, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229471479, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229471536, "dur": 320, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229471920, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229471976, "dur": 441, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229472560, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229472607, "dur": 284, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229473048, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229473195, "dur": 269, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229473517, "dur": 61, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229473626, "dur": 197, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229473916, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229473919, "dur": 227, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229474203, "dur": 49, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229474351, "dur": 201, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229474554, "dur": 54, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229474611, "dur": 378, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229475040, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229475097, "dur": 305, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229475405, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229475407, "dur": 526, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229476405, "dur": 165, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229476752, "dur": 225, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229477059, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229477106, "dur": 183, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229477326, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229477367, "dur": 127, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229477540, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229477589, "dur": 211, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229477839, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229477842, "dur": 181, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229478068, "dur": 46, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229478155, "dur": 105, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229478291, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229478378, "dur": 252, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229478675, "dur": 34, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229478780, "dur": 920, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229479707, "dur": 76, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229480020, "dur": 263, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229480397, "dur": 46, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229480526, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229480680, "dur": 50, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229480831, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229480872, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229480874, "dur": 97, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229481000, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229481125, "dur": 194, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229481363, "dur": 43, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229481455, "dur": 285, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229481785, "dur": 44, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229481866, "dur": 229, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229482176, "dur": 77, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229482301, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229482677, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229482726, "dur": 224, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229482989, "dur": 72, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229483135, "dur": 329, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229483512, "dur": 40, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229483555, "dur": 228, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229483798, "dur": 241, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229484149, "dur": 64, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229484254, "dur": 491, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229484848, "dur": 66, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229484981, "dur": 216, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229485277, "dur": 32, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229485351, "dur": 86, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229485476, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229485513, "dur": 110, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229485648, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229485650, "dur": 206, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229485902, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229485981, "dur": 207, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229486277, "dur": 42, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229486404, "dur": 982, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229487572, "dur": 45, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229487619, "dur": 257, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229487928, "dur": 105, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229488138, "dur": 293, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229488607, "dur": 61, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229488788, "dur": 360, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229489151, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229489268, "dur": 89382, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229578656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229578659, "dur": 199, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229578862, "dur": 23, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229578887, "dur": 10772, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229589665, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229589669, "dur": 200, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229589873, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229589875, "dur": 1235, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229591115, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229591117, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229591298, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229591301, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229591320, "dur": 1371, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229592696, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229592698, "dur": 210, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229592911, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229592913, "dur": 109, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229593175, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229593177, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229593206, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229593208, "dur": 912, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229594124, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229594152, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229594153, "dur": 610, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229594767, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229594802, "dur": 669, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229595474, "dur": 259, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229595738, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229596821, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229596825, "dur": 264, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229597092, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229597094, "dur": 421, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229597518, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229597520, "dur": 198, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229597724, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229597753, "dur": 676, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229598434, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229598637, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229598639, "dur": 455, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229599229, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229599232, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229599266, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229599464, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229599488, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229599490, "dur": 482, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229599976, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229599991, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229600242, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229600547, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229600549, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229600789, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229600791, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229600808, "dur": 196, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229601007, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229601034, "dur": 592, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229601629, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229601641, "dur": 270, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229602070, "dur": 217, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229602290, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229602314, "dur": 331, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229602650, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229602816, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229602818, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229602846, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229603087, "dur": 166, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229603256, "dur": 440, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229603701, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229603729, "dur": 176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229603910, "dur": 41914, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229645840, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229645848, "dur": 232, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229646083, "dur": 16, "ph": "X", "name": "ProcessMessages 2100", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229646100, "dur": 51391, "ph": "X", "name": "ReadAsync 2100", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229697804, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229697809, "dur": 394, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229698394, "dur": 3208, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229701609, "dur": 7066, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229708686, "dur": 5, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229708694, "dur": 101421, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229810124, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229810129, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229810188, "dur": 38, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229810227, "dur": 27321, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229837557, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229837560, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229837615, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229837619, "dur": 2148, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229839774, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229839777, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229839822, "dur": 29, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229839853, "dur": 52627, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229892488, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229892491, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229892548, "dur": 264, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229892814, "dur": 8873, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229901701, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229901706, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229901762, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768229901766, "dur": 113612, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230015387, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230015391, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230015425, "dur": 29, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230015455, "dur": 25135, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230040599, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230040602, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230040663, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230040667, "dur": 942, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230041616, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230041619, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230041682, "dur": 44, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230041729, "dur": 638, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230042371, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230042373, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230042410, "dur": 14, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230042427, "dur": 652, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230043086, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230043132, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753768230043134, "dur": 12774, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 22624, "tid": 387224, "ts": 1753768230056614, "dur": 1077, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 22624, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 22624, "tid": 21474836480, "ts": 1753768229389813, "dur": 328107, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 22624, "tid": 21474836480, "ts": 1753768229717921, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 22624, "tid": 21474836480, "ts": 1753768229717923, "dur": 64, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 22624, "tid": 387224, "ts": 1753768230057694, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 22624, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 22624, "tid": 17179869184, "ts": 1753768229370086, "dur": 685866, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 22624, "tid": 17179869184, "ts": 1753768229370223, "dur": 19510, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 22624, "tid": 17179869184, "ts": 1753768230055955, "dur": 64, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 22624, "tid": 17179869184, "ts": 1753768230055970, "dur": 21, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 22624, "tid": 17179869184, "ts": 1753768230056020, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 22624, "tid": 387224, "ts": 1753768230057707, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753768229399350, "dur": 60, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753768229399454, "dur": 2244, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753768229401716, "dur": 1187, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753768229403117, "dur": 89, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753768229403206, "dur": 356, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753768229403715, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7F3FD0BA415121F8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229403900, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_0A4F51C79A281E51.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229404472, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_CFCACFCB0B6C8B75.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229405191, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_E8070D706152CF3C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229405622, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D55CC3D658EA7477.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229405814, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_653E29E5346DF570.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229406759, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_78F35D34EC6436FD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229408686, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753768229409621, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_436C0E2610862891.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229409744, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753768229410149, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_45FC2729272CD115.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229410298, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_757AC0409981C6B8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229410490, "dur": 163, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229411303, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753768229412210, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753768229412574, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229412935, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753768229413551, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753768229413808, "dur": 287, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753768229414100, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753768229414982, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753768229415424, "dur": 170, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AdaptivePerformance.ref.dll_F07A6251CDFF0652.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229415877, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753768229416234, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753768229417053, "dur": 222, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753768229418408, "dur": 196, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753768229418630, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753768229418719, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753768229419001, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753768229419486, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753768229421292, "dur": 717, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753768229423109, "dur": 190, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753768229428098, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753768229428864, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16942202937359522267.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753768229431663, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753768229431905, "dur": 225, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753768229433029, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753768229433978, "dur": 267, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753768229435428, "dur": 261, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753768229436355, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753768229437255, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753768229403598, "dur": 34843, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753768229438456, "dur": 603909, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753768230042366, "dur": 138, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753768230042608, "dur": 128, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753768230042843, "dur": 104, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753768230042994, "dur": 1719, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753768229404012, "dur": 34528, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229438557, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229438637, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_4E3884B8FC8ED343.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229438718, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_4E3884B8FC8ED343.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229438832, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229438888, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E74EDDF9CB117B30.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229439071, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229439441, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4B2BD6EE7377B869.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229439685, "dur": 563, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753768229439684, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4221B8E55F8CA2CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229441166, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753768229441298, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229441443, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229441588, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229441646, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753768229442313, "dur": 618, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1753768229443324, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1753768229443724, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753768229444721, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18308057294943403080.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753768229444843, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229445208, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229445838, "dur": 1150, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\DebugManager.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229445422, "dur": 2315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229447738, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229448250, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229448763, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229449239, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229449758, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229450250, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229450747, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229451315, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229451799, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229452275, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229452767, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229453260, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229453826, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229454329, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229454824, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229455340, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229455833, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229456708, "dur": 847, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Description\\IUnitDescriptor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229456320, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229457646, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229458140, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229458621, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229459119, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229459598, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229460108, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229460601, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229461399, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229461894, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229462435, "dur": 946, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Average.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229462388, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229464002, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229464531, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229465021, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229465529, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229466016, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229466505, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229467042, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229468135, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Subsystem.Registration.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229468260, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229468347, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229469500, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditorModule\\SpriteFrameModule\\SpriteFrameModule.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229468486, "dur": 1344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229469830, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229470057, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229470190, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229470626, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229470764, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229471250, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\BoxTool.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229471929, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\GridPaintingState.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229472694, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\TilePaletteSaveUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229472758, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\TileUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229473298, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteBrushesDropdownToggle.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229473399, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteBrushesPopup.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229473471, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteBrushInspector.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229473525, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteBrushInspectorElement.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229473587, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteBrushInspectorPopup.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229473695, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteClipboardButton.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229473749, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteClipboardElement.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229473830, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteContextMenuHandler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229473912, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteDragHandler.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229470877, "dur": 3201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229474078, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229474192, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229474313, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229474621, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229474702, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229474810, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229475154, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229475330, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229475653, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229475783, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753768229475747, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229476297, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229476419, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229476791, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229477034, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229477269, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229477649, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229478157, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229478723, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetMenu\\AssetsSelection.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229479216, "dur": 567, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetsUtils\\Processor\\PlasticAssetsProcessor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229479785, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetsUtils\\Processor\\UnityCloudProjectLinkMonitor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229480003, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetsUtils\\Processor\\WorkspaceOperationsMonitor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229480075, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetsUtils\\ProjectPath.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229480154, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetsUtils\\RefreshAsset.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229480367, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetsUtils\\SaveAssets.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229481220, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Developer\\UpdateProgress.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229482008, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Hub\\ParseArguments.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229483411, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\EditorDispatcher.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229483652, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\GetPlasticShortcut.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229484627, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\Tree\\TreeHeaderSettings.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229484957, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\UnityPlasticTimer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229485080, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\UnityStyles.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229485426, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Branch\\BranchesSelection.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229485873, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Changesets\\ChangesetsViewMenu.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229485982, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Changesets\\DateFilter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229486108, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\ConfirmContinueWithPendingChangesDialog.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229486189, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\CreateWorkspace\\CreateWorkspaceView.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229486260, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\CreateWorkspace\\CreateWorkspaceViewState.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229486350, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\CreateWorkspace\\Dialogs\\CreateRepositoryDialog.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229486778, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Diff\\GetClientDiffInfos.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229487086, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\History\\HistoryListHeaderState.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753768229478270, "dur": 9165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229487436, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229487575, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753768229487963, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229488357, "dur": 68, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229488528, "dur": 208729, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229703276, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753768229702907, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229703847, "dur": 67, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229703927, "dur": 188750, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229898301, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753768229898299, "dur": 1704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753768229901546, "dur": 278, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753768229901845, "dur": 113767, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753768230040625, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753768230040624, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753768230040783, "dur": 1044, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753768230041833, "dur": 571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229404249, "dur": 34380, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229438637, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229438630, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_5A4DAB7FB6024184.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753768229438704, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229438762, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229438760, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_A9BA431DC9D6DC43.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753768229438869, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_A9BA431DC9D6DC43.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753768229438995, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229439428, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229439427, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3042BE781365D6CF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753768229439498, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229439582, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229439580, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_167389B99B5142D0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753768229440065, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229440242, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229440704, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753768229440910, "dur": 284, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229441212, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229441351, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229441597, "dur": 528, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229442305, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229442408, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229442744, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229442815, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229442909, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229442968, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229443454, "dur": 322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229443822, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229443909, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229444147, "dur": 782, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229444986, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229445053, "dur": 5130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229450205, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229450290, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229451730, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\TestCommandBuilder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229440815, "dur": 12592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229453408, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229453550, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229453648, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753768229454163, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229456697, "dur": 654, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229457353, "dur": 911, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229458671, "dur": 375, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229459973, "dur": 364, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\CommandLineTest\\ISettingsBuilder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229460615, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\GuiHelper.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229460761, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\TestListBuilder\\ResultSummarizer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229461296, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\RequireApiProfileAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229461733, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlatformSetup\\XboxOnePlatformSetup.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229462419, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRun\\Tasks\\SaveUndoIndexTask.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229462588, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRun\\Tasks\\TestTaskBase.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229462694, "dur": 834, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRun\\TestJobData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229463808, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Callbacks\\WindowResultUpdaterDataHolder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229464192, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\EditorCompilationInterfaceProxy.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229464349, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\IEditorCompilationInterfaceProxy.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229464502, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestRunner\\Utils\\ITestListCache.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229453756, "dur": 11495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229465252, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229465337, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229465412, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229465510, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753768229465665, "dur": 1266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229466932, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229467071, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753768229467544, "dur": 837, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.21\\Runtime\\BurstExecutionEnvironment.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229468659, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.21\\Runtime\\CompilerServices\\SkipLocalsInitAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229468905, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.21\\Runtime\\FunctionPointer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229469353, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.21\\Runtime\\Intrinsics\\x86\\Avx.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229469741, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.21\\Runtime\\Intrinsics\\x86\\Sse3.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753768229467175, "dur": 2769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229469945, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229470104, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229470189, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753768229470487, "dur": 804, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229470332, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229471485, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229471640, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229471699, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1753768229472314, "dur": 691, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229473407, "dur": 105402, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1753768229588006, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229589663, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229589783, "dur": 1430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Subsystem.Registration.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229591213, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229591291, "dur": 1454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229592746, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229592837, "dur": 1411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229594249, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229594306, "dur": 1427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229595734, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229596700, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscorrc.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229595805, "dur": 1729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229597536, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229597652, "dur": 1615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229599268, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229599344, "dur": 1485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229600829, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229600898, "dur": 1483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229602382, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229602441, "dur": 1470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229603911, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229603977, "dur": 1512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229605495, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229607054, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229605567, "dur": 1777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229607344, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229608190, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Localization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229608426, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.WebEncoders.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229607415, "dur": 1828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229609244, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229609916, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229610605, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229610861, "dur": 92050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229702915, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229702913, "dur": 1729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229706253, "dur": 279, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753768229706594, "dur": 103719, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753768229837610, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229837609, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229837786, "dur": 2215, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753768229840006, "dur": 202234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229404024, "dur": 34530, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229438580, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229438565, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A0DAE67C58E4E2BB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229438737, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229438735, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_840091757717660B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229438897, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_6DBABB005B232272.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229439066, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229439065, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C8D7D3E3F1064FA2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229439657, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229439656, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_BFCB0FCACAF77119.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229439837, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229440081, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229440874, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229440944, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229441009, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229441104, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229441245, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753768229441595, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753768229441740, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229441915, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229441978, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753768229442044, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229443006, "dur": 347, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753768229443354, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753768229443488, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229444083, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753768229444243, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229444310, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753768229444436, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229444520, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229444648, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229444700, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753768229444826, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229445234, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3818794330681902346.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753768229445505, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229446031, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229446836, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229447468, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229448569, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229449035, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229450169, "dur": 7208, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\Graphics.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229449524, "dur": 7924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229457449, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229458191, "dur": 2582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\Wizard.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229458001, "dur": 3092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229461094, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229461584, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229462067, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229462602, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229463698, "dur": 2129, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Events\\ManualEventUnit.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229463179, "dur": 2675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229465855, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229466335, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229466831, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229467433, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229467583, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229468600, "dur": 1537, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229470249, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float3x2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229470339, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float3x3.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229470534, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float4x4.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229470617, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Geometry\\MinMaxAABB.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229470994, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int3x2.gen.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229471207, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\math.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229471288, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\math_unity_conversion.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229471429, "dur": 525, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Noise\\cellular2x2.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229471955, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Noise\\cellular2x2x2.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229467666, "dur": 5049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229472716, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229472923, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1753768229472847, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229473007, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229473073, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229473450, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229473552, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229473980, "dur": 1292, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections\\NativeStream.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753768229473664, "dur": 1639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229475303, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229475434, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229475627, "dur": 1154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229476787, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229477312, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229477566, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229478413, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229479220, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229479601, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229479730, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229479805, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229479994, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229480142, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229480368, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229480597, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229480706, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229480788, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229480910, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229481154, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229481381, "dur": 2488, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229481243, "dur": 2967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229484211, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229484988, "dur": 332, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229484466, "dur": 1231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229485697, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229486025, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229487306, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229487577, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753768229487736, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229487865, "dur": 100145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229588011, "dur": 1623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229589635, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229589731, "dur": 3274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229599473, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229599719, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229599960, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229593008, "dur": 7526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229600539, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229601010, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229600893, "dur": 1781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229602674, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229602881, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229603326, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229603538, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229603968, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.CommandLine.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229602952, "dur": 1950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229604902, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229604969, "dur": 1510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229606480, "dur": 775, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229607988, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.JSInterop.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229607263, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229609109, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229609306, "dur": 680, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753768229609181, "dur": 2118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753768229611299, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229611410, "dur": 226205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753768229837617, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753768229837616, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753768229837806, "dur": 2212, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753768229840020, "dur": 202192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229403975, "dur": 34520, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229438527, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229438778, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5CE5D6336EE197AE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229438861, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5CE5D6336EE197AE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229438933, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229438931, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_34BE634AB49D7D0F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229438993, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229439141, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229439139, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_22170A5568CA4919.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229439209, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229439297, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229439296, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4CE912B968344AF2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229439377, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229439638, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229439697, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229439696, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_AE499E41BD2D7290.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229439884, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229439883, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_7B7D5A6F18794603.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229439990, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229439989, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_2A1DCE08184F097D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229440042, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229440581, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753768229440698, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229440811, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229441299, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229441357, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229441665, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229441786, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229441862, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229442203, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229442348, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229442405, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229442521, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229442586, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229442688, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229442760, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229442907, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229443130, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229443187, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229443244, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229443299, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229443395, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229444079, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229444134, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229444342, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229444414, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229444731, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229445037, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229445104, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229445184, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229445241, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229445664, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\unityplastic.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229445870, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventSystem.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229445933, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229446223, "dur": 1030, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229447756, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229447817, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\IClipRegion.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229448099, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IGraphicEnabledDisabled.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229440882, "dur": 8636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229449520, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229449667, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229450139, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229450611, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229451117, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229451592, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229452070, "dur": 467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229452537, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229453014, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229453505, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229454541, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229455163, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229455648, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229456142, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229456681, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.adaptiveperformance@4.0.1\\Runtime\\VisualScript\\ClusterInfo.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229456610, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229457915, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229458402, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229459292, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229459821, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229460761, "dur": 3121, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\IndividualPropertyDrawer.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229460358, "dur": 3702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229464060, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229464789, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229465268, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229465758, "dur": 1815, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\RuntimeCodebase.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229465758, "dur": 2987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229468746, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229468900, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229469244, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229469370, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229469474, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229470023, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229470288, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229470511, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229470612, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229470720, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229470800, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229471529, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\FastAction.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229471683, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TextMeshProUGUI.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229471785, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMPro_MeshUtilities.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229471850, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMPro_Private.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229471908, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMPro_UGUI_Private.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229472107, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_ColorGradient.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229472161, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_Compatibility.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229472216, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_CoroutineTween.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229472270, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_DefaultControls.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229472329, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_Dropdown.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229472536, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_FontFeatureTable.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229472608, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_InputField.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229472875, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_ResourcesManager.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229473004, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_SelectionCaret.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229473067, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_Settings.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229473118, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_ShaderUtilities.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229473235, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_SpriteAnimator.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229471004, "dur": 2782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229473787, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229473871, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229474015, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229475261, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229474077, "dur": 1510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229475587, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229475702, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229475768, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229476526, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229477113, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229477267, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229477357, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229477576, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229478266, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229478562, "dur": 3156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753768229481772, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229482156, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229482263, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229482555, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229482665, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229483010, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229483104, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229483399, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229483532, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229483650, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229483484, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229484035, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229484732, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\Sequence\\Jog.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229484990, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\Sequence\\MarkerHeaderTrackManipulator.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753768229484170, "dur": 1315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229485486, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229485621, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229485756, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229485819, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229486019, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229486718, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229487249, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229487588, "dur": 100426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229588016, "dur": 1627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229589644, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229591350, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229589775, "dur": 1676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229591451, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229591513, "dur": 1593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229593107, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229593192, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229593446, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229593245, "dur": 1583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229594829, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229596006, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229596221, "dur": 662, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229597045, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229594921, "dur": 3539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229598461, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229598564, "dur": 1701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229600265, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229600336, "dur": 1746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229602083, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229602155, "dur": 1682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229603837, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229603909, "dur": 1515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229605425, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229606521, "dur": 1952, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753768229605483, "dur": 3474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229608958, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229609089, "dur": 1370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753768229610460, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229610867, "dur": 107394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229718262, "dur": 4338, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753768229722601, "dur": 319725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229404055, "dur": 34511, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229438591, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753768229438650, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1753768229438577, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5735A0C9A2F205F9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229438763, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229438911, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5735A0C9A2F205F9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229439649, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753768229439645, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_C49F1B1E1B36C13A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229439865, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229439986, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229440075, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229440549, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229440937, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753768229441573, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753768229442139, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229442220, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229442988, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229443074, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229443868, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753768229443926, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229444000, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229444624, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229444734, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753768229445018, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753768229445276, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229445864, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753768229445430, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229446492, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229447390, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229447880, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229448425, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229448922, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229449425, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229450247, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229450963, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229451465, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229451965, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229452458, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229452944, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229453422, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229453911, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229454407, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229454889, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229455405, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229455903, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229456698, "dur": 678, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.adaptiveperformance@4.0.1\\Runtime\\VisualScript\\OnThermalMetricUnit.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753768229456407, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229457591, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229458076, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229458633, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229459132, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229459663, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229460164, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229460648, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229461235, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229461742, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229462513, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarPerSecond.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753768229462231, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229463368, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229463868, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229464417, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229464914, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229465452, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229465958, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229466448, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229466938, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229467431, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229467562, "dur": 358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753768229470197, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsContext.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753768229470324, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsConverter.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753768229470582, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsDirectConverter.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753768229471253, "dur": 310, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\EditorBinding\\EditorTimeBinding.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753768229471564, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\EditorBinding\\ExpectedTypeAttribute.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753768229471776, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorDelayedAttribute.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753768229467544, "dur": 4901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229472446, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229472592, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229472763, "dur": 994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229473758, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229473890, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229474557, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229474633, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229475285, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229475526, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229475591, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229476004, "dur": 1414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229477424, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229477786, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229477925, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229478038, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229478335, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229478489, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229478615, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229478721, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229478881, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229478987, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229479101, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229479180, "dur": 839, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229480019, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229480078, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229480574, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229480648, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229480764, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229480875, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229480979, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229481215, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229481653, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229481752, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229482084, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229482178, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229482530, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229482615, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229482920, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229483002, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229483309, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229483681, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753768229483451, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229484002, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229484121, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229484444, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229484537, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229484868, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229484968, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229485276, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229485517, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229485653, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753768229485645, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229486126, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229486211, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229486717, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229487251, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229487579, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753768229487933, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229488265, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229488358, "dur": 99650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229588010, "dur": 1647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229589658, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229589759, "dur": 1472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229591232, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229591302, "dur": 1494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229592797, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229592861, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229594139, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229594219, "dur": 1433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229595653, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229596832, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Authorization.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753768229595717, "dur": 2111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229597829, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229597904, "dur": 1549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229599454, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229599557, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229601076, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229602314, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753768229601143, "dur": 1702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229602845, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229602917, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229602973, "dur": 1599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229604573, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229604654, "dur": 1551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229606206, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229606273, "dur": 1421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229607695, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229607751, "dur": 1499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753768229609250, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229609850, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229610254, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229610857, "dur": 519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768229611422, "dur": 429209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753768230040634, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753768230040633, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753768230040815, "dur": 1309, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753768229404135, "dur": 34445, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229438600, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753768229438661, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1753768229438589, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_CFCACFCB0B6C8B75.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229438748, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229438913, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7C14B74751CEB14A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229439649, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753768229439648, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_AC815CCA2EC74CAA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229439743, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753768229439742, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E4099628EC59A0DB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229440068, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753768229440067, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_014553A2310FB6CF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229441021, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.21\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753768229441020, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A9FCAC3B680B6215.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229441169, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229441704, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753768229444106, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753768229444727, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753768229444888, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229445207, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229445357, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3648465134513896125.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753768229445831, "dur": 793, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753768229445597, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229446910, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229447414, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229447907, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229448389, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229448863, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229449240, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229449728, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229450229, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229450721, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229451199, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229451681, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229452190, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229452693, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229453195, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229453741, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229454239, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229454735, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229455234, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229455740, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229456708, "dur": 662, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\FlowGraphUnitUISample.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753768229456248, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229457445, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229457992, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229458493, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229459198, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229459701, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229460199, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229460677, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229461302, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229461815, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229462504, "dur": 647, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Multiply.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753768229462301, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229463915, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229464427, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229464911, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229465416, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229465549, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229466422, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229466921, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229467996, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229468112, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229468409, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229468559, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Subsystem.Registration.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229469022, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229469210, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Subsystem.Registration.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229469279, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229469350, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229469526, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229470178, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229470448, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229470617, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229470734, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229470838, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229471935, "dur": 500, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Evaluation\\InfiniteRuntimeClip.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753768229472964, "dur": 1026, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Playables\\ITimeControl.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753768229470947, "dur": 3716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229474664, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229474785, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229474901, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229475019, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229475382, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229475818, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753768229475938, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@3.1.3\\Runtime\\GridInformation\\GridInformation.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753768229476050, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@3.1.3\\Runtime\\Tiles\\HexagonalRuleTile\\HexagonalRuleTile.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753768229475477, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229476306, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229476401, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229476828, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229476911, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229477217, "dur": 3888, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229481165, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229481292, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229483681, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.InternalAPIEngineBridge.001.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753768229481348, "dur": 2451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229483799, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229483956, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229484350, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229484472, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229484873, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229485486, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753768229485728, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229486030, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229486116, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229486614, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229487248, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229487580, "dur": 100432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229588014, "dur": 1642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229589657, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229589751, "dur": 1478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229591230, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229591299, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229592763, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229592833, "dur": 1454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229594288, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229594352, "dur": 1482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229595835, "dur": 1112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229596958, "dur": 1509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229598467, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229598570, "dur": 1539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229600110, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229600191, "dur": 1511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229601703, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229601775, "dur": 1485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229603265, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229603331, "dur": 1677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229605009, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229605093, "dur": 1570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229606663, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229606732, "dur": 1401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229608133, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229608255, "dur": 1477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753768229609732, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229609845, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229610871, "dur": 111735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753768229722606, "dur": 320094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753768230052246, "dur": 3442, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 22624, "tid": 387224, "ts": 1753768230057764, "dur": 1521, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 22624, "tid": 387224, "ts": 1753768230059355, "dur": 1051, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 22624, "tid": 387224, "ts": 1753768230056590, "dur": 3858, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}