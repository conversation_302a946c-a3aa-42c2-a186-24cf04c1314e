{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 22624, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 22624, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 22624, "tid": 345525, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 22624, "tid": 345525, "ts": 1753767745435990, "dur": 11, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 22624, "tid": 345525, "ts": 1753767745436014, "dur": 5, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 22624, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 22624, "tid": 1, "ts": 1753767744592593, "dur": 2300, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22624, "tid": 1, "ts": 1753767744594897, "dur": 30081, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 22624, "tid": 1, "ts": 1753767744624980, "dur": 147562, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 22624, "tid": 345525, "ts": 1753767745436021, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 22624, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744592562, "dur": 19989, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744612553, "dur": 822502, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744612565, "dur": 211, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744612780, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744612783, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744612990, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744612993, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744613040, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744613049, "dur": 3830, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744616886, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744616889, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744616982, "dur": 2, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744616985, "dur": 81, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617070, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617073, "dur": 92, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617168, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617171, "dur": 54, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617228, "dur": 1, "ph": "X", "name": "ProcessMessages 1116", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617231, "dur": 50, "ph": "X", "name": "ReadAsync 1116", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617284, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617286, "dur": 42, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617332, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617334, "dur": 43, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617380, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617382, "dur": 71, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617456, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617458, "dur": 68, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617530, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617532, "dur": 63, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617598, "dur": 1, "ph": "X", "name": "ProcessMessages 1273", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617600, "dur": 40, "ph": "X", "name": "ReadAsync 1273", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617646, "dur": 71, "ph": "X", "name": "ReadAsync 9", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617720, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617723, "dur": 50, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617785, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617788, "dur": 46, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617836, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617839, "dur": 43, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617885, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617888, "dur": 45, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617936, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617938, "dur": 50, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617992, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744617994, "dur": 39, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618035, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618038, "dur": 41, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618082, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618084, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618133, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618135, "dur": 46, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618184, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618186, "dur": 45, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618235, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618237, "dur": 44, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618283, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618285, "dur": 49, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618337, "dur": 2, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618340, "dur": 44, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618387, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618390, "dur": 46, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618439, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618441, "dur": 44, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618488, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618490, "dur": 45, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618538, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618540, "dur": 44, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618588, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618590, "dur": 46, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618639, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618642, "dur": 41, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618686, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618688, "dur": 44, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618736, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618738, "dur": 44, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618785, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618787, "dur": 46, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618836, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618838, "dur": 46, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618887, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618889, "dur": 44, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618936, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618938, "dur": 43, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618984, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744618986, "dur": 43, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619032, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619034, "dur": 44, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619081, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619083, "dur": 44, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619130, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619132, "dur": 44, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619179, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619181, "dur": 42, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619226, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619228, "dur": 36, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619270, "dur": 46, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619320, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619322, "dur": 45, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619370, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619372, "dur": 46, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619421, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619423, "dur": 46, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619471, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619473, "dur": 41, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619517, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619519, "dur": 44, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619566, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619568, "dur": 45, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619618, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619620, "dur": 45, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619668, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619670, "dur": 45, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619718, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619720, "dur": 42, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619766, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619769, "dur": 44, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619815, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619818, "dur": 42, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619862, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619864, "dur": 45, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619912, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619914, "dur": 43, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619961, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744619963, "dur": 114, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620080, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620082, "dur": 47, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620131, "dur": 2, "ph": "X", "name": "ProcessMessages 1634", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620133, "dur": 33, "ph": "X", "name": "ReadAsync 1634", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620169, "dur": 2, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620173, "dur": 43, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620219, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620221, "dur": 47, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620271, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620273, "dur": 20, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620295, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620296, "dur": 8, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620309, "dur": 14, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620326, "dur": 37, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620368, "dur": 43, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620414, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620417, "dur": 40, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620460, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620462, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620513, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620516, "dur": 46, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620566, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620569, "dur": 43, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620614, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620616, "dur": 44, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620664, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620666, "dur": 61, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620732, "dur": 17, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620752, "dur": 82, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620839, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620886, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620889, "dur": 48, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620941, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620943, "dur": 51, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744620998, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621001, "dur": 49, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621052, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621054, "dur": 30, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621088, "dur": 70, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621160, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621162, "dur": 47, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621213, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621215, "dur": 46, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621264, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621267, "dur": 33, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621302, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621304, "dur": 44, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621351, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621354, "dur": 47, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621404, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621406, "dur": 137, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621548, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621622, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621625, "dur": 73, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621701, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621704, "dur": 68, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621775, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621778, "dur": 68, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621848, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621850, "dur": 64, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621921, "dur": 71, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621995, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744621997, "dur": 67, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622067, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622069, "dur": 42, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622115, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622117, "dur": 43, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622163, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622165, "dur": 45, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622214, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622216, "dur": 42, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622262, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622264, "dur": 48, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622315, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622317, "dur": 42, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622363, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622365, "dur": 43, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622411, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622413, "dur": 43, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622459, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622461, "dur": 67, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622531, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622533, "dur": 44, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622580, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622582, "dur": 44, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622630, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622631, "dur": 33, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622667, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622669, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622714, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622716, "dur": 38, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622757, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622759, "dur": 42, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622805, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622807, "dur": 103, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622913, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622915, "dur": 23, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622941, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744622944, "dur": 73, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623021, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623076, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623078, "dur": 33, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623115, "dur": 1, "ph": "X", "name": "ProcessMessages 111", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623119, "dur": 57, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623179, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623181, "dur": 75, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623259, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623262, "dur": 38, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623302, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623304, "dur": 43, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623364, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623368, "dur": 44, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623415, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623417, "dur": 35, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623457, "dur": 47, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623508, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623511, "dur": 40, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623554, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623556, "dur": 69, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623629, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623631, "dur": 41, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623675, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623677, "dur": 40, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623720, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623722, "dur": 91, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623816, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623818, "dur": 50, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623872, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744623874, "dur": 137, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624014, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624017, "dur": 66, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624085, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624088, "dur": 51, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624142, "dur": 1, "ph": "X", "name": "ProcessMessages 985", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624144, "dur": 47, "ph": "X", "name": "ReadAsync 985", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624195, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624197, "dur": 67, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624268, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624270, "dur": 73, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624346, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624349, "dur": 69, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624421, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624423, "dur": 44, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624471, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624473, "dur": 41, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624517, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624519, "dur": 41, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624563, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624565, "dur": 67, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624635, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624638, "dur": 50, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624691, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624693, "dur": 38, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624735, "dur": 41, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624779, "dur": 1, "ph": "X", "name": "ProcessMessages 142", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624781, "dur": 40, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624825, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624827, "dur": 38, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624868, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624870, "dur": 38, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624911, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624913, "dur": 46, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624962, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744624964, "dur": 42, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625009, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625011, "dur": 41, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625055, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625056, "dur": 44, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625104, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625106, "dur": 37, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625147, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625149, "dur": 43, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625195, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625197, "dur": 44, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625244, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625246, "dur": 42, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625291, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625293, "dur": 43, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625339, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625341, "dur": 38, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625382, "dur": 1, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625384, "dur": 39, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625426, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625428, "dur": 41, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625472, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625474, "dur": 41, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625518, "dur": 1, "ph": "X", "name": "ProcessMessages 106", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625520, "dur": 46, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625569, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625571, "dur": 42, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625616, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625619, "dur": 41, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625662, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625664, "dur": 41, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625708, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625710, "dur": 40, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625753, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625756, "dur": 41, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625800, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625802, "dur": 46, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625850, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625852, "dur": 45, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625900, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625902, "dur": 44, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625949, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625951, "dur": 40, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625994, "dur": 2, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744625997, "dur": 41, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626042, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626044, "dur": 45, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626092, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626094, "dur": 44, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626142, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626144, "dur": 43, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626190, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626192, "dur": 42, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626236, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626238, "dur": 40, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626281, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626285, "dur": 45, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626333, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626335, "dur": 43, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626380, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626383, "dur": 44, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626430, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626432, "dur": 46, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626481, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626483, "dur": 41, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626527, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626529, "dur": 43, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626574, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626576, "dur": 45, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626625, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626627, "dur": 43, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626673, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626675, "dur": 44, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626722, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626724, "dur": 37, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626765, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626767, "dur": 106, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626878, "dur": 35, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626916, "dur": 1, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626917, "dur": 35, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744626957, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627009, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627011, "dur": 49, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627063, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627065, "dur": 45, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627112, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627114, "dur": 41, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627159, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627161, "dur": 41, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627205, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627207, "dur": 40, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627250, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627252, "dur": 45, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627300, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627302, "dur": 43, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627348, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627352, "dur": 43, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627398, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627400, "dur": 42, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627445, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627447, "dur": 41, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627494, "dur": 42, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627539, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627541, "dur": 44, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627588, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627590, "dur": 44, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627637, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627639, "dur": 42, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627685, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627687, "dur": 45, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627734, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627736, "dur": 40, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627779, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627781, "dur": 40, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627824, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627826, "dur": 44, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627873, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627875, "dur": 44, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627922, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627924, "dur": 46, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627973, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744627975, "dur": 39, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628016, "dur": 1, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628018, "dur": 41, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628062, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628064, "dur": 44, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628111, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628113, "dur": 44, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628161, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628163, "dur": 42, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628208, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628210, "dur": 43, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628256, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628258, "dur": 44, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628304, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628306, "dur": 33, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628342, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628344, "dur": 224, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628572, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628620, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628622, "dur": 45, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628670, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628672, "dur": 44, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628719, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628721, "dur": 40, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628764, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628766, "dur": 45, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628814, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628816, "dur": 37, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628856, "dur": 1, "ph": "X", "name": "ProcessMessages 33", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628858, "dur": 46, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628907, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628909, "dur": 44, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628956, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744628958, "dur": 52, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629013, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629015, "dur": 44, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629063, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629065, "dur": 42, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629110, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629112, "dur": 42, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629157, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629159, "dur": 45, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629207, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629209, "dur": 43, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629255, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629257, "dur": 41, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629302, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629303, "dur": 46, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629353, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629355, "dur": 33, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629391, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629393, "dur": 44, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629440, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629441, "dur": 43, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629488, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629490, "dur": 93, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629588, "dur": 52, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629643, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629645, "dur": 39, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629688, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629690, "dur": 35, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629728, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629730, "dur": 41, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629774, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629776, "dur": 30, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629808, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629810, "dur": 52, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629866, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629918, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629921, "dur": 43, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629968, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744629970, "dur": 44, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630017, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630019, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630044, "dur": 38, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630087, "dur": 40, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630130, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630132, "dur": 33, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630170, "dur": 44, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630217, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630219, "dur": 44, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630266, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630268, "dur": 44, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630315, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630317, "dur": 44, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630364, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630366, "dur": 44, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630412, "dur": 3, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630416, "dur": 40, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630460, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630461, "dur": 37, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630502, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630504, "dur": 43, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630550, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630552, "dur": 44, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630599, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630601, "dur": 39, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630643, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630645, "dur": 77, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630724, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630726, "dur": 47, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630776, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630778, "dur": 45, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630826, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630828, "dur": 47, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630878, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630881, "dur": 46, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630929, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630932, "dur": 42, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630976, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744630978, "dur": 26, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631007, "dur": 34, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631044, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631046, "dur": 42, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631091, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631093, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631114, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631185, "dur": 45, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631233, "dur": 1, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631236, "dur": 72, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631315, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631341, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631343, "dur": 39, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631386, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631405, "dur": 41, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631449, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631451, "dur": 84, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631540, "dur": 37, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631580, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631582, "dur": 69, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631656, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631703, "dur": 47, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631752, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631754, "dur": 31, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631788, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631790, "dur": 39, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631833, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631835, "dur": 68, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631906, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631908, "dur": 41, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631952, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744631954, "dur": 43, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632002, "dur": 43, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632049, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632051, "dur": 31, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632085, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632086, "dur": 40, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632131, "dur": 1, "ph": "X", "name": "ProcessMessages 42", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632133, "dur": 40, "ph": "X", "name": "ReadAsync 42", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632177, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632179, "dur": 43, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632226, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632228, "dur": 50, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632280, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632282, "dur": 47, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632332, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632334, "dur": 44, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632381, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632383, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632449, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632450, "dur": 43, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632497, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632499, "dur": 72, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632575, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632577, "dur": 72, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632659, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632662, "dur": 62, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632727, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632728, "dur": 75, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632806, "dur": 1, "ph": "X", "name": "ProcessMessages 1071", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632808, "dur": 48, "ph": "X", "name": "ReadAsync 1071", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632860, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632862, "dur": 50, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632915, "dur": 1, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632917, "dur": 55, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744632976, "dur": 35, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633014, "dur": 1, "ph": "X", "name": "ProcessMessages 94", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633016, "dur": 42, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633063, "dur": 45, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633110, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633113, "dur": 55, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633172, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633174, "dur": 47, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633224, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633226, "dur": 44, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633273, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633275, "dur": 24, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633301, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633342, "dur": 53, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633398, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633400, "dur": 41, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633445, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633447, "dur": 46, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633496, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633498, "dur": 41, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633542, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633544, "dur": 42, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633589, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633592, "dur": 47, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633643, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633645, "dur": 43, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633691, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633693, "dur": 75, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633771, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633773, "dur": 54, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633830, "dur": 1, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633832, "dur": 89, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633924, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633926, "dur": 44, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633974, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744633976, "dur": 38, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634019, "dur": 43, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634065, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634067, "dur": 45, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634114, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634116, "dur": 44, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634163, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634165, "dur": 41, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634209, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634211, "dur": 44, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634258, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634260, "dur": 43, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634306, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634308, "dur": 42, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634353, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634355, "dur": 40, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634398, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634400, "dur": 43, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634446, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634448, "dur": 44, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634495, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634497, "dur": 44, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634543, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634545, "dur": 40, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634588, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634590, "dur": 46, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634638, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634640, "dur": 39, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634682, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634684, "dur": 45, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634733, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634735, "dur": 92, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634830, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634832, "dur": 44, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634878, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634880, "dur": 41, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634924, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744634992, "dur": 53, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635048, "dur": 3, "ph": "X", "name": "ProcessMessages 1461", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635052, "dur": 37, "ph": "X", "name": "ReadAsync 1461", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635092, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635095, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635138, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635140, "dur": 45, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635188, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635190, "dur": 43, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635236, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635239, "dur": 45, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635286, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635288, "dur": 47, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635338, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635340, "dur": 44, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635386, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635390, "dur": 42, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635434, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635436, "dur": 44, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635483, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635485, "dur": 31, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635520, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635522, "dur": 44, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635568, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635570, "dur": 45, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635619, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635621, "dur": 38, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635662, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635664, "dur": 45, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635712, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635714, "dur": 46, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635764, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635765, "dur": 36, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635804, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635806, "dur": 42, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635851, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635853, "dur": 44, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635900, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635902, "dur": 43, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635948, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635950, "dur": 45, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744635998, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636000, "dur": 42, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636045, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636047, "dur": 42, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636092, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636094, "dur": 47, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636144, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636146, "dur": 37, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636185, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636187, "dur": 43, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636233, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636235, "dur": 69, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636307, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636309, "dur": 65, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636378, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636380, "dur": 73, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636456, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636458, "dur": 74, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636536, "dur": 1, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636538, "dur": 65, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636606, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636608, "dur": 48, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636659, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636661, "dur": 116, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636781, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636783, "dur": 55, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636842, "dur": 1, "ph": "X", "name": "ProcessMessages 1067", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636844, "dur": 46, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636894, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636896, "dur": 49, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636947, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636949, "dur": 31, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744636984, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637022, "dur": 43, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637067, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637069, "dur": 44, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637116, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637118, "dur": 33, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637154, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637156, "dur": 39, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637198, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637200, "dur": 54, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637257, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637260, "dur": 47, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637309, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637311, "dur": 45, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637359, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637361, "dur": 75, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637441, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637516, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637518, "dur": 50, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637571, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637573, "dur": 44, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637620, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637622, "dur": 31, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637657, "dur": 66, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637727, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637729, "dur": 52, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637784, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637786, "dur": 68, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637857, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637859, "dur": 51, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637913, "dur": 1, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637915, "dur": 64, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637982, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744637985, "dur": 71, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638059, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638061, "dur": 43, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638107, "dur": 1, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638109, "dur": 46, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638157, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638161, "dur": 126, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638290, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638292, "dur": 46, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638341, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638343, "dur": 46, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638392, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638394, "dur": 41, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638437, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638439, "dur": 38, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638481, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638483, "dur": 40, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638526, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638528, "dur": 45, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638576, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638578, "dur": 38, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638618, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638620, "dur": 46, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638669, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638671, "dur": 42, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638717, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638719, "dur": 44, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638766, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638768, "dur": 36, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638806, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638808, "dur": 45, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638856, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638858, "dur": 42, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638903, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638905, "dur": 46, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638954, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744638956, "dur": 41, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744639000, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744639002, "dur": 40, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744639046, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744639049, "dur": 41, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744639093, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744639094, "dur": 47, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744639145, "dur": 3107, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744642262, "dur": 455, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744642720, "dur": 17, "ph": "X", "name": "ProcessMessages 17538", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744642738, "dur": 62, "ph": "X", "name": "ReadAsync 17538", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744642804, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744642806, "dur": 40, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744642849, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744642852, "dur": 98, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744642955, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643003, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643006, "dur": 43, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643052, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643054, "dur": 41, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643098, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643100, "dur": 92, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643197, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643245, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643247, "dur": 42, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643292, "dur": 53, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643348, "dur": 85, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643436, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643438, "dur": 128, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643571, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643624, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643626, "dur": 69, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643699, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643701, "dur": 39, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643744, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643746, "dur": 104, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643854, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643907, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643912, "dur": 44, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643959, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744643962, "dur": 39, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644004, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644006, "dur": 134, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644144, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644195, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644197, "dur": 47, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644246, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644248, "dur": 62, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644313, "dur": 3, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644318, "dur": 85, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644408, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644467, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644470, "dur": 74, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644547, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644549, "dur": 119, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644673, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644727, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644729, "dur": 69, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644802, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644804, "dur": 113, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644922, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644965, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744644971, "dur": 39, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645013, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645014, "dur": 35, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645053, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645055, "dur": 32, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645089, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645091, "dur": 137, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645233, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645291, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645293, "dur": 51, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645347, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645349, "dur": 40, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645392, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645394, "dur": 89, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645487, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645522, "dur": 40, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645569, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645571, "dur": 45, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645619, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645621, "dur": 40, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645664, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645666, "dur": 124, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645797, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645862, "dur": 2, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645866, "dur": 43, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645914, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645916, "dur": 60, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645979, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744645980, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646039, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646086, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646088, "dur": 61, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646152, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646154, "dur": 44, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646200, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646202, "dur": 81, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646289, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646360, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646362, "dur": 45, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646409, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646412, "dur": 43, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646457, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646459, "dur": 67, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646529, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646531, "dur": 57, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646593, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646642, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646644, "dur": 41, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646688, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646690, "dur": 40, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646733, "dur": 1, "ph": "X", "name": "ProcessMessages 117", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646735, "dur": 44, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646782, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646784, "dur": 69, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646855, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646861, "dur": 43, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646907, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646909, "dur": 41, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646953, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744646955, "dur": 64, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647023, "dur": 1, "ph": "X", "name": "ProcessMessages 111", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647025, "dur": 39, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647067, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647069, "dur": 52, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647125, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647201, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647203, "dur": 43, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647249, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647251, "dur": 96, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647351, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647428, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647430, "dur": 43, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647488, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647490, "dur": 83, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647578, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647648, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647650, "dur": 43, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647695, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647698, "dur": 104, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647806, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647878, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647928, "dur": 46, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647977, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744647979, "dur": 74, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648058, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648128, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648130, "dur": 43, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648176, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648178, "dur": 102, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648285, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648353, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648355, "dur": 44, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648402, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648404, "dur": 37, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648448, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648450, "dur": 109, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648563, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648612, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648614, "dur": 106, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648722, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648724, "dur": 59, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648878, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744648881, "dur": 273, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649156, "dur": 2, "ph": "X", "name": "ProcessMessages 2178", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649159, "dur": 61, "ph": "X", "name": "ReadAsync 2178", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649223, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649225, "dur": 37, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649274, "dur": 37, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649314, "dur": 1, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649316, "dur": 32, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649351, "dur": 30, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649384, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649386, "dur": 45, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649435, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649436, "dur": 111, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649552, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649692, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649695, "dur": 41, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649747, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649764, "dur": 45, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649840, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649843, "dur": 120, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649966, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744649968, "dur": 41, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744650042, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744650044, "dur": 72, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744650119, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744650121, "dur": 36, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744650160, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744650161, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744650278, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744650280, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744650348, "dur": 2, "ph": "X", "name": "ProcessMessages 1081", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744650351, "dur": 99, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744656903, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744656912, "dur": 592, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744657512, "dur": 24, "ph": "X", "name": "ProcessMessages 20503", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744657538, "dur": 117, "ph": "X", "name": "ReadAsync 20503", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744657658, "dur": 47, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744657709, "dur": 155, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744657870, "dur": 289, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658163, "dur": 2, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658165, "dur": 35, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658203, "dur": 1, "ph": "X", "name": "ProcessMessages 109", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658206, "dur": 18, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658226, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658227, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658259, "dur": 38, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658300, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658303, "dur": 115, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658421, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658424, "dur": 92, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658521, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658559, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658561, "dur": 227, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658819, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744658821, "dur": 241, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659065, "dur": 2, "ph": "X", "name": "ProcessMessages 1877", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659069, "dur": 202, "ph": "X", "name": "ReadAsync 1877", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659281, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659283, "dur": 74, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659531, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659535, "dur": 74, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659622, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659624, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659806, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659808, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659891, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744659893, "dur": 152, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660091, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660094, "dur": 147, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660288, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660345, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660489, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660492, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660639, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660641, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660807, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660808, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660925, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744660928, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661055, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661058, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661121, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661124, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661250, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661252, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661422, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661425, "dur": 172, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661600, "dur": 27, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661630, "dur": 59, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661692, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661755, "dur": 73, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661838, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661841, "dur": 72, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661938, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744661940, "dur": 57, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662031, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662034, "dur": 72, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662151, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662153, "dur": 87, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662248, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662250, "dur": 70, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662358, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662360, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662397, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662398, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662450, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662649, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662652, "dur": 140, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662954, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744662957, "dur": 235, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744663242, "dur": 4, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744663247, "dur": 175, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744663570, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744663574, "dur": 133, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744663710, "dur": 3, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744663714, "dur": 50, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744663766, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744663769, "dur": 121, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744663893, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744663940, "dur": 58, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664042, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664045, "dur": 115, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664205, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664209, "dur": 174, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664393, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664397, "dur": 55, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664455, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664458, "dur": 58, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664519, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664521, "dur": 136, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664890, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744664894, "dur": 85, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665017, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665021, "dur": 68, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665092, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665094, "dur": 134, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665266, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665269, "dur": 72, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665444, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665448, "dur": 115, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665657, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665661, "dur": 58, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665722, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665727, "dur": 137, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665901, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744665903, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744666040, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744666043, "dur": 186, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744666231, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744666233, "dur": 130, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744666436, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744666458, "dur": 157, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744666676, "dur": 63, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744666803, "dur": 228, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667034, "dur": 55, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667092, "dur": 66, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667205, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667208, "dur": 101, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667312, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667315, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667345, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667347, "dur": 98, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667519, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667521, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667579, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667582, "dur": 123, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744667709, "dur": 23139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744690856, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744690860, "dur": 300, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744691346, "dur": 5, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744691353, "dur": 483, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744691840, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744691843, "dur": 548, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744692674, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744692677, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744692718, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744692720, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744692758, "dur": 501, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744693444, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744693446, "dur": 323, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744694028, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744694032, "dur": 315, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744694570, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744694687, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744694841, "dur": 37, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744695026, "dur": 176, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744695204, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744695372, "dur": 131, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744695621, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744695624, "dur": 146, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744695774, "dur": 304, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744696176, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744696178, "dur": 293, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744696590, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744696593, "dur": 231, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744696939, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744696943, "dur": 285, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744697343, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744697345, "dur": 237, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744697785, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744697909, "dur": 277, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744698298, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744698301, "dur": 323, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744698727, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744698730, "dur": 269, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744699191, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744699193, "dur": 274, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744699871, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744699942, "dur": 427, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744700602, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744700605, "dur": 226, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744701274, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744701276, "dur": 203, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744701481, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744701483, "dur": 270, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744701933, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744701936, "dur": 217, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744702157, "dur": 421, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744702783, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744702786, "dur": 340, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744703130, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744703135, "dur": 393, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744703891, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744704238, "dur": 1299, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744705832, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744705911, "dur": 611, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744706777, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744707068, "dur": 304, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744707378, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744707381, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744707436, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744707440, "dur": 89206, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744796654, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744796661, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744796684, "dur": 28, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744796713, "dur": 10839, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744807559, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744807563, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744807592, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744807803, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744807836, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744807838, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744807940, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744807942, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744807976, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744808145, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744808189, "dur": 811, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744809006, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744809008, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744809028, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744809106, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744809132, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744809134, "dur": 943, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810083, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810119, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810121, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810292, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810357, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810360, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810563, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810594, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810597, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810657, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810694, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744810696, "dur": 511, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744811212, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744811242, "dur": 1017, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744812263, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744812265, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744812301, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744812303, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744812366, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744812396, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744812499, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744812529, "dur": 472, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744813004, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744813008, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744813040, "dur": 796, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744813841, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744813872, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744813873, "dur": 430, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744814309, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744814346, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744814376, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744814378, "dur": 226, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744814608, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744814637, "dur": 333, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744814974, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744815010, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744815012, "dur": 784, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744815801, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744815833, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744816046, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744816078, "dur": 366, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744816449, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744816473, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744816567, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744816598, "dur": 221, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744816825, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744816860, "dur": 618, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744817482, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744817513, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744817515, "dur": 343, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744817861, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744817889, "dur": 442, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744818336, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744818372, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744818374, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744818409, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744818411, "dur": 819, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744819235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744819238, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744819278, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744819279, "dur": 772, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744820057, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744820145, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744820147, "dur": 272, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744820424, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744820459, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744820592, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744820612, "dur": 277, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744820895, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744820922, "dur": 543, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744821469, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744821471, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744821508, "dur": 814, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744822327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744822329, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744822368, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744822370, "dur": 133, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744822508, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744822544, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744822546, "dur": 515, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744823065, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744823067, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744823102, "dur": 1013, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744824121, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744824125, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744824149, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744824151, "dur": 121, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744824276, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744824277, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744824312, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744824314, "dur": 229, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744824547, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744824575, "dur": 635, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744825216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744825218, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744825253, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744825255, "dur": 1059, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826319, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826321, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826359, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826361, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826510, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826546, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826666, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826667, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826700, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826703, "dur": 287, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744826995, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744827029, "dur": 1017, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828050, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828084, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828197, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828227, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828257, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828259, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828290, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828317, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828349, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828378, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828403, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828435, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828464, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828490, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828521, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828565, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828597, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828599, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828644, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828674, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828708, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828744, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828746, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828775, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828777, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828854, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828857, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828888, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828892, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828925, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828952, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744828985, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829014, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829016, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829044, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829074, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829076, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829107, "dur": 24, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829135, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829166, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829168, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829199, "dur": 25, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829227, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829256, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829259, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829288, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829290, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829317, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829319, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829352, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829387, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829415, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829446, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829448, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829470, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829518, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829555, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829556, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829586, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829587, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829620, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829622, "dur": 31, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829657, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829659, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829691, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829724, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829728, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829792, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829794, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829825, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829827, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829860, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829896, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829926, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744829962, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830001, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830002, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830038, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830040, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830077, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830079, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830119, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830149, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830177, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830211, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830213, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830252, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744830254, "dur": 757, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744831019, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744831022, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744831075, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744831078, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744831121, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744831123, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744831164, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767744831165, "dur": 393499, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745224673, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745224676, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745224734, "dur": 102, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745224837, "dur": 6008, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745230855, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745230860, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745230939, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745230941, "dur": 3842, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745234796, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745234800, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745234852, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745234856, "dur": 154, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745235016, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745235062, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745235064, "dur": 137562, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745372642, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745372647, "dur": 769, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745373422, "dur": 31, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745373455, "dur": 266, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745373726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745373728, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745373748, "dur": 17, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745373766, "dur": 27722, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745401498, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745401502, "dur": 482, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745401990, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745401994, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745402040, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745402044, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745402095, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745402096, "dur": 2380, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745404488, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745404492, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745404549, "dur": 37, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745404588, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745404606, "dur": 10110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745414724, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745414728, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745414781, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 22624, "tid": 25769803776, "ts": 1753767745414784, "dur": 20263, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 22624, "tid": 345525, "ts": 1753767745436033, "dur": 2971, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 22624, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 22624, "tid": 21474836480, "ts": 1753767744592520, "dur": 180052, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 22624, "tid": 21474836480, "ts": 1753767744772573, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 22624, "tid": 21474836480, "ts": 1753767744772576, "dur": 59, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 22624, "tid": 345525, "ts": 1753767745439008, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 22624, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 22624, "tid": 17179869184, "ts": 1753767744585150, "dur": 849945, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 22624, "tid": 17179869184, "ts": 1753767744585290, "dur": 6900, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 22624, "tid": 17179869184, "ts": 1753767745435097, "dur": 65, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 22624, "tid": 17179869184, "ts": 1753767745435113, "dur": 20, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 22624, "tid": 345525, "ts": 1753767745439018, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753767744612780, "dur": 61, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753767744612883, "dur": 2547, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753767744615448, "dur": 1120, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753767744616751, "dur": 82, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753767744616833, "dur": 348, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753767744617341, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_7F3FD0BA415121F8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744617595, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_3FF536AA263283DD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744617714, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_55CE00BEA3C83003.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744617971, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5735A0C9A2F205F9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744619124, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4CE912B968344AF2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744620518, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_CF17D4CA2A31CD8C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744621047, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744623798, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744623947, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744624307, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744624710, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744625064, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753767744625719, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744625858, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753767744627201, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744629866, "dur": 130, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753767744630002, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744630071, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753767744630598, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753767744631359, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744631441, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744631626, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744631748, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744631840, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744632056, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_16F22BA31A1D34C0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744632345, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744632477, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753767744633302, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744634263, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753767744634369, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753767744635149, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753767744635434, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744637319, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753767744637685, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753767744638014, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753767744638409, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744638545, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744638819, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753767744642731, "dur": 408, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Notifications.Android.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753767744644566, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753767744644827, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753767744645885, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Common.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753767744646215, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753767744647071, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1753767744649476, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744649649, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753767744654074, "dur": 3811, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.Simulator.Extension.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753767744658248, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.Simulator.Extension.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753767744658389, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753767744658520, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753767744659296, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753767744659459, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753767744617217, "dur": 42498, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753767744659731, "dur": 754906, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753767745414804, "dur": 227, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753767745415105, "dur": 4958, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753767744617480, "dur": 42285, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744659826, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744659936, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5735A0C9A2F205F9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744660104, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_CFCACFCB0B6C8B75.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744661354, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744661353, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_DF7D9FD794808934.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744661772, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_845FBD1B5AF442B3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744662158, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744662234, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744662233, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_EF20AB2F56E66DDF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744662322, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744662457, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744662456, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_4A99E7ADB31DB747.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744662583, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744662582, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_014553A2310FB6CF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744662784, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744662783, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_CEA62C1281A7BDC9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744663009, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744663008, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744663277, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744663359, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744663467, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744663718, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744664039, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744664220, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744664299, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744664364, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744664424, "dur": 696, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744665497, "dur": 705, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744666267, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744666344, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744666461, "dur": 1125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744667676, "dur": 784, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744668475, "dur": 362, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744668934, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744669504, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744669610, "dur": 2112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744663617, "dur": 10449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767744674068, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744674235, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744674910, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744675401, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744675952, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744676682, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744677170, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744677674, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744678276, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744679488, "dur": 734, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\CreateStructDescriptor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744679074, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744680326, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744680815, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744681301, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744681810, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744682324, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744682816, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744683414, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744683917, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744684413, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744684904, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744685398, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744685935, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744686481, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744687154, "dur": 1157, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\UI\\UnityOnScrollRectValueChangedMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744688895, "dur": 788, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTriggerEnterMessageListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744686957, "dur": 2769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744689767, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744689879, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744690009, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.iOS.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767744690676, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744690768, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744690890, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744691103, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744691207, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744691367, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744691486, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744692170, "dur": 345, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Animation\\ICurvesOwner.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744692791, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Audio\\AudioTrack.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744693121, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Evaluation\\ScheduleRuntimeClip.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744693423, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Events\\Signals\\SignalEmitter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744694170, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Utilities\\FrameRate.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744694233, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Utilities\\HashUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744694438, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Utilities\\TimelineUndo.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744691634, "dur": 3066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767744694700, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744694813, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767744695164, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744695256, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767744695673, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744696322, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.pixel-perfect@5.1.0\\Runtime\\PixelPerfectCamera.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744695793, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767744696414, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744696858, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744696987, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744697053, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767744697468, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744697745, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744698594, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\AssetOverlays\\Cache\\LockStatusCache.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744700270, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Gluon\\UpdateReport\\UpdateReportListView.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744700584, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\OrganizationsInformation.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744700634, "dur": 1140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\ParentWindow.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744703876, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\StatusBar\\StatusBar.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744704004, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\TabButton.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744704473, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\UI\\Tree\\TreeViewItemIds.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744705290, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Branch\\Dialogs\\DeleteBranchDialog.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744705346, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Branch\\Dialogs\\RenameBranchDialog.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744705559, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\Changesets\\DateFilter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753767744697852, "dur": 8198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767744706052, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744706235, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753767744706633, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744706917, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767744706740, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767744707414, "dur": 159, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767744707734, "dur": 517354, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767745230837, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767745230365, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753767745231080, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767745231198, "dur": 1813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753767745235124, "dur": 279, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767745235420, "dur": 137584, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753767745401589, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753767745401587, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753767745401833, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753767745401904, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767745401903, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753767745402131, "dur": 305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753767745402130, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753767745404777, "dur": 75, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753767745402462, "dur": 2410, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1753767745404880, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744617480, "dur": 42267, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744659789, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744660436, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744660430, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_A9BA431DC9D6DC43.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744660517, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744660864, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744660863, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_3A0FC4B426FE4E30.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744661006, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744661071, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_3A0FC4B426FE4E30.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744661337, "dur": 465, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744661336, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_5577D3E1632C1B08.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744662240, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744662322, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744662321, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_78F35D34EC6436FD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744662564, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744662732, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744662731, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_FB383BF5D7743A79.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744662873, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744662872, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744663057, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744663458, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753767744663602, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744663601, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_45FC2729272CD115.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744663775, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753767744663974, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744664112, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753767744664590, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744665470, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753767744665741, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744666021, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744666123, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753767744666426, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753767744666534, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744666619, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744666741, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744666841, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753767744667070, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744667318, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13938709970307456411.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753767744667557, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6210570011482043853.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753767744667720, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744667843, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744667962, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744668924, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744669406, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744670013, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744670486, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744670964, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744671510, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744671985, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744672495, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744673000, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744673496, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744674664, "dur": 1228, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mobile.android-logcat@1.4.5\\Editor\\AndroidLogcatMemoryViewer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753767744675892, "dur": 722, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mobile.android-logcat@1.4.5\\Editor\\AndroidLogcatManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753767744673986, "dur": 2767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744676753, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744677260, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744678287, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744678813, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744679483, "dur": 787, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.adaptiveperformance@4.0.1\\Runtime\\Subsystem\\AdaptivePerformanceSubsystem.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753767744679331, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744680587, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744681063, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744681570, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744682070, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744682543, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744683024, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744683518, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744684008, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744684488, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744684993, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744685480, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744685989, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744686515, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744687001, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744687534, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744688084, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744689529, "dur": 402, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744691079, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Collections\\MergedList.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753767744692166, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsPrimitiveConverter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753767744692284, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsReflectedConverter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753767744692400, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsTypeConverter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753767744692818, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\Keyframe_DirectConverter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753767744688213, "dur": 5438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744693652, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744693788, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744695001, "dur": 302, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarPerSecond.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753767744695318, "dur": 1035, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarSubtract.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753767744693900, "dur": 2738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744696639, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744696830, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744697050, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744697528, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744697626, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744697699, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744698049, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744697861, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744698513, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744698690, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744698809, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744698886, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744699201, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744699334, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744699444, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744699558, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744699667, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744699796, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744699902, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744700256, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744700417, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744701007, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744701452, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744701204, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744701687, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744701826, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744702165, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744702269, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744702992, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744703195, "dur": 2655, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744705866, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744706237, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753767744706466, "dur": 72018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744778485, "dur": 27721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744806839, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744806208, "dur": 1922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.iOS.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744808131, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744808560, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744808944, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744808216, "dur": 2187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744810404, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744811026, "dur": 601, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-convert-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744810487, "dur": 2143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744812630, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744812703, "dur": 1536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744814240, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744815280, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-errorhandling-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744814715, "dur": 1670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744816386, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744816455, "dur": 1703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744818158, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744819291, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.OpenSsl.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744819542, "dur": 354, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744818276, "dur": 2093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744820370, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744820461, "dur": 2173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744822635, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744822753, "dur": 1648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744824402, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744825318, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744826000, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744824512, "dur": 2107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744826620, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744826716, "dur": 1663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744828380, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744828981, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Google.Protobuf.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744829246, "dur": 275, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.Kestrel.Core.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744830249, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744830428, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753767744828460, "dur": 2692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753767744831154, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744831404, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753767744831526, "dur": 582850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744617497, "dur": 42333, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744659860, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744659842, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A0DAE67C58E4E2BB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744659968, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744661278, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744661276, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4B2BD6EE7377B869.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744661339, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744661498, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744661599, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744661597, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_AEBE2A22DC1E5B26.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744661717, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744661789, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_AEBE2A22DC1E5B26.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744662323, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744662524, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744662523, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_8F5A15826918C0F2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744662719, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744662859, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744662964, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744663026, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744663025, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_493D590627D79599.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744664363, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753767744665412, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744666296, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744666391, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753767744666444, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753767744667028, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744667229, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744667555, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2499735490941455596.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753767744667619, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753767744667861, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5806762800881712256.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753767744667913, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744668018, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744668847, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744669341, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744669852, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744670352, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744671516, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744672524, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744673164, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744673690, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744674397, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744674970, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744675848, "dur": 1103, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1.1.9\\Editor\\Aseprite\\AsepriteReader.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753767744676951, "dur": 1423, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1.1.9\\Editor\\Aseprite\\AsepriteFile.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753767744675471, "dur": 2995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744678467, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744679484, "dur": 802, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Description\\UnitDescription.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753767744679163, "dur": 1283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744680446, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744680943, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744681734, "dur": 1055, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Reflection\\CodebaseSubset.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753767744681426, "dur": 1574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744683000, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744683509, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744684001, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744684508, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744684986, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744685475, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744685998, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744686628, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744687464, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744688408, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744688581, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744688885, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744688976, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Subsystem.Registration.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744689240, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744689328, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744689497, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744689467, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744689898, "dur": 740, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744690921, "dur": 647, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744690683, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744691936, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744692021, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744692294, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744692382, "dur": 378, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744692136, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744693027, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744693139, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744693226, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744693597, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744693355, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744693867, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744694023, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744694192, "dur": 339, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744694825, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.2.0\\Editor\\LayoutOverlay\\DropdownMenu.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753767744694146, "dur": 1114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744695261, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744695765, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@3.1.3\\Runtime\\GridInformation\\GridInformation.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753767744695916, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap.extras@3.1.3\\Runtime\\Tiles\\RuleOverrideTile\\AdvancedRuleOverrideTile.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753767744695417, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744696029, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744696344, "dur": 2168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744696158, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744698636, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744698776, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744698921, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744699034, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744699143, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744699215, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744699369, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744699546, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744699723, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744699806, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744700323, "dur": 403, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744699887, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744700825, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744700933, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744701239, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744701325, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744701714, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744701822, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744702257, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744702398, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744702776, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744703022, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753767744703140, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744703456, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744703545, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744704417, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744705021, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744705797, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744706473, "dur": 100687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744808270, "dur": 611, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744807162, "dur": 2156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744809319, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744809772, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744811254, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744811464, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Lib\\Editor\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744809408, "dur": 2129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744811537, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744812361, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Mail.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744812452, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744811621, "dur": 1729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744813351, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744814265, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Console.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744813419, "dur": 1781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744815201, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744816699, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744815385, "dur": 1769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744817155, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744817233, "dur": 1480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744818714, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744819540, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Hosting.Abstractions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744818777, "dur": 2097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744820875, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744821127, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744821000, "dur": 1648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744822649, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744823553, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744824225, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744822730, "dur": 1875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744824605, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744825155, "dur": 403, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744824689, "dur": 2075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744826766, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744828711, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744829056, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753767744827074, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753767744829345, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744829916, "dur": 523, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753767744830444, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753767744830649, "dur": 581480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744617530, "dur": 42986, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744660535, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744660526, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5CE5D6336EE197AE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744660695, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744660988, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_6DBABB005B232272.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744661135, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744661133, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_8182EAE46932F6EA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744661272, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744661370, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744661369, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_838DFC2B1332ABCB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744661789, "dur": 839, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D55CC3D658EA7477.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744662631, "dur": 377, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744662630, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FF05A8CB0537AF9E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744663010, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744663214, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_FF05A8CB0537AF9E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744663343, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744663716, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744663835, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744663966, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744664037, "dur": 340, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744664418, "dur": 1063, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744665499, "dur": 628, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744666128, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744666243, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744666344, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744666462, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744666562, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744666658, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744667016, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744667444, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744667500, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744667561, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744667706, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744667845, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744667961, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744668035, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744668185, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744668381, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744668921, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744669159, "dur": 374, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744670497, "dur": 388, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTestAttribute.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744671041, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744671168, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744672324, "dur": 806, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744673404, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestEnumeratorWrapper.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744674130, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ITestRunCallback.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744663440, "dur": 11264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744674705, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744674840, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744675840, "dur": 872, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744677554, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744677725, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744677790, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744679472, "dur": 672, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\Analytics\\AnalyticsReporter.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744680237, "dur": 501, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\CallbacksDelegator.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744680739, "dur": 1056, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\CallbacksDelegatorListener.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744682029, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\Api\\ITestResultAdaptor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744674944, "dur": 11157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744686102, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744686242, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744687250, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\MaskEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744687303, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\MenuOptions.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744687695, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\SelfControllerEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744686459, "dur": 1464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744687928, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744688083, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744689465, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.21\\Runtime\\Intrinsics\\x86\\Avx.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753767744688206, "dur": 1772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744689979, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744690144, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753767744690261, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744690640, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744690988, "dur": 261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744691555, "dur": 701, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744692355, "dur": 261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744690732, "dur": 1956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1753767744692732, "dur": 1571, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744695036, "dur": 102035, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1753767744806201, "dur": 1663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744807865, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744807941, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744809422, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744809519, "dur": 1452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744810972, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744811065, "dur": 1529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744812594, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744812671, "dur": 1507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744814178, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744814275, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744815277, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744814257, "dur": 1879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744816137, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744816652, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744816214, "dur": 1604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744817819, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744817898, "dur": 1530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744819442, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744819633, "dur": 1574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mobile.AndroidLogcat.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744821208, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744821300, "dur": 1538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744822839, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744824247, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744822919, "dur": 1522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744824442, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744825318, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.EventLog.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744825607, "dur": 619, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744826318, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753767744824521, "dur": 2488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744827010, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744827080, "dur": 1497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753767744828581, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744829186, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744830186, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744830377, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753767744830640, "dur": 581267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744617609, "dur": 42517, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744660142, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744660133, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_5A4DAB7FB6024184.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744660210, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744661335, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744661439, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744662076, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_177D521712D70A28.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744662200, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744662410, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744662409, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_C1FF8119698EE88F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744662536, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744662721, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744662892, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744662955, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744662954, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744663096, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744663095, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_A28F69477662F91E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744663477, "dur": 307, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753767744664140, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753767744664289, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744664360, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753767744664846, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753767744665049, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744665216, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744665296, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744665465, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1753767744665621, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744665688, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744665759, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744665851, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753767744665967, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744666078, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753767744667085, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10341329905002649615.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753767744667150, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744667245, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744667354, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753767744667422, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744667502, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5521740788080646575.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753767744667991, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744668733, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744669271, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744669773, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744670282, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744670757, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744671322, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744671809, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744672296, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744672832, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744673449, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744673943, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744674520, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744675033, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744675512, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744676295, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744676847, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744677365, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744678239, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744678740, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744679495, "dur": 674, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.adaptiveperformance@4.0.1\\Runtime\\VisualScript\\SetPerformanceLevelsUnit.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744679233, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744680425, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744680913, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744681459, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744681965, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744682584, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744683102, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744683631, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744684134, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744684624, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744685121, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744685638, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744686134, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744686672, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744687171, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744687672, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744688368, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744689479, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float2x4.gen.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744689577, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float3x2.gen.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744689983, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Il2CppEagerStaticClassConstructionAttribute.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744690885, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\PropertyAttributes.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744691106, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\uint2x3.gen.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744691211, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\uint3.gen.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744688497, "dur": 3032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744691529, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744691718, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744691827, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744692130, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744692406, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744692568, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744692747, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744693108, "dur": 399, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744692685, "dur": 868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744693554, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744693648, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744693755, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744693875, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744694461, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Debugging\\GraphDebugDataProvider.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744694737, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Description\\IMachineDescription.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744694949, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\Special\\UnknownInspector.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744695261, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_3_0.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753767744694035, "dur": 1634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744695674, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744695810, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744696180, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744696294, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744696439, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744696799, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744696894, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744697012, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744697566, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744697690, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744697809, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744698219, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744698376, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744698456, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744698533, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744698650, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744698749, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744698833, "dur": 1850, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744700689, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744701060, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744701158, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.Unified.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744701502, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744701597, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744701958, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744702057, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744702431, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744702557, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744703048, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744703162, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744703488, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744703574, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744704124, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744705045, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744705789, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744706312, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753767744706404, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744706468, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744706890, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744707083, "dur": 99585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744806669, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Unified.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744808187, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744808369, "dur": 2241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744810610, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744810695, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744812356, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744810751, "dur": 1878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744812629, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744814281, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744812795, "dur": 1861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744814656, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744815279, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744814783, "dur": 1827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744816610, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744816986, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744816833, "dur": 1851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744818685, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744819540, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\clrjit.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744820454, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744818799, "dur": 2986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744821786, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744821877, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744823395, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744823491, "dur": 1781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744825273, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744826918, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753767744825618, "dur": 1698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744827317, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744827405, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753767744828884, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744829184, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744829267, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744829858, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744829948, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744830083, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744830206, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744830326, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744830473, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744830596, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744831413, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753767744831480, "dur": 581438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744617707, "dur": 42528, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744660244, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_1D18347A9CFA8727.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744661298, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C8D7D3E3F1064FA2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744661569, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_1C70BFBBAC69830A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744661662, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744661661, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4CE912B968344AF2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744661791, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744661790, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_914656F6FBA8F5C6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744661950, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744662014, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744662013, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_EEC6715D6C078ED4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744662592, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744662590, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6BBAA4C46A2C16BA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744662966, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744662964, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744663361, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753767744663590, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744664392, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753767744664709, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744664769, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744665367, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744665438, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753767744666111, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753767744667964, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744668736, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744669362, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744669893, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744670396, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744671201, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744671766, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744672264, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744672805, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744673313, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744673799, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744674293, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744674768, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744675249, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744675884, "dur": 826, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.State\\States\\StateAnalyser.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744675732, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744677045, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744677570, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744678045, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744678537, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744679471, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Control\\ForAnalyser.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744679028, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744680133, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744680652, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744681147, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744681646, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744682151, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744682622, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744683114, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744683630, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744684135, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744684640, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744685127, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744685616, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744686107, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744686596, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744687079, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744687998, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744688492, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Subsystem.Registration.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744688611, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744689461, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditor\\SpriteEditorUtility.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744689901, "dur": 346, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditorModule\\SpriteOutlineModule.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744688728, "dur": 1650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744690382, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744690503, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744690617, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744690972, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744691097, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744691457, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744692171, "dur": 378, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMPro_UGUI_Private.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744693439, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_SpriteAssetImportFormats.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744693638, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_SubMeshUI.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744693848, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_TextProcessingStack.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744691568, "dur": 2460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744694029, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744694113, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744694244, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744694727, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744695570, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744695902, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\GridSelection\\GridSelectionRotationTool.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744696091, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\MoveTool.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744696202, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\RotateTool.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744696310, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\EditorTools\\TilemapEditorToolPreferences.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744697229, "dur": 471, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\TilePaletteSaveUtility.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744697976, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilemapEditorToolButton.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744698151, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteActiveTargetsDropdownMenu.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744698358, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteBrushesDropdownMenu.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744698460, "dur": 413, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteBrushesPopup.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744698911, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteBrushInspectorElement.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744699035, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@1.0.0\\Editor\\UI\\TilePaletteClipboardButton.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744695198, "dur": 4131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744699329, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744699497, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744699607, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Notifications.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744699758, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744699882, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mobile.AndroidLogcat.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753767744700004, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744700335, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744700426, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744700745, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744700852, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744701201, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744701283, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744701612, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744701914, "dur": 269, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744701735, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744702392, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744702526, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744702899, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744703474, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744703849, "dur": 867, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.8.2\\Editor\\Views\\PendingChanges\\Changelists\\ChangelistMenu.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753767744703848, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744705480, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744705788, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744706446, "dur": 67271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744773720, "dur": 4759, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744778480, "dur": 27724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744806575, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744807934, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744808265, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744806206, "dur": 2273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744808480, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744808543, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744808604, "dur": 2289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Subsystem.Registration.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744810894, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744811033, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744811614, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744810976, "dur": 1781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744812758, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744814296, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744812911, "dur": 2031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744814942, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744815280, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744815038, "dur": 1874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744816912, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744816997, "dur": 1664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744818662, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744819260, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744819541, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Features.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744818744, "dur": 1767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744820512, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744821004, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744820849, "dur": 4036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744824886, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744825318, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\PlaybackEngines\\MetroSupport\\UnityEditor.UWP.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767744825023, "dur": 1814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744826838, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744826924, "dur": 1612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Notifications.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767744828536, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744828660, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744828844, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744828983, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744829154, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744829493, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744829566, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744829656, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744829875, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744830214, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744830443, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744830558, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767744830637, "dur": 399736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767745230692, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767745231280, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.62f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767745230376, "dur": 2635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767745234618, "dur": 418, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753767745235270, "dur": 138853, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753767745401953, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767745401952, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753767745402135, "dur": 2810, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1753767745430799, "dur": 4054, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 22624, "tid": 345525, "ts": 1753767745439087, "dur": 254822, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 22624, "tid": 345525, "ts": 1753767745694002, "dur": 1129, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 22624, "tid": 345525, "ts": 1753767745436004, "dur": 259177, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}