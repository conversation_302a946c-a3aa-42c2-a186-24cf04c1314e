using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Base : MonoBehaviour
{
    [Header("Base Settings")]
    public Color baseColor = Color.green;
    
    private SpriteRenderer spriteRenderer;
    
    void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
        if (spriteRenderer == null)
        {
            spriteRenderer = gameObject.AddComponent<SpriteRenderer>();
        }
        
        // Create a simple square sprite
        CreateSquareSprite();
    }
    
    void Start()
    {
        // Ensure the base is tagged properly
        if (gameObject.tag != "Base")
        {
            gameObject.tag = "Base";
        }
    }
    
    private void CreateSquareSprite()
    {
        // Create a simple 1x1 white texture
        Texture2D texture = new Texture2D(1, 1);
        texture.SetPixel(0, 0, Color.white);
        texture.Apply();
        
        // Create sprite from texture
        Sprite sprite = Sprite.Create(texture, new Rect(0, 0, 1, 1), new Vector2(0.5f, 0.5f), 1f);
        spriteRenderer.sprite = sprite;
        spriteRenderer.color = baseColor;
        
        // Set size
        transform.localScale = new Vector3(1f, 1f, 1f);
    }
}
