# Unity Editor Setup Guide - Geometric Defenders

## Overview
This guide provides step-by-step instructions for setting up the Geometric Defenders game in Unity Editor after the core scripts have been implemented.

## Prerequisites
- Unity 2021 LTS or newer
- 2D project template
- Scripts folder already contains all required C# files

## Step 1: Project Settings

### 1.1 Create Required Tags
1. Go to **Edit → Project Settings → Tags and Layers**
2. Under **Tags**, add the following:
   - `Enemy`
   - `Bullet`
   - `Base`

## Step 2: Scene Setup

### 2.1 Configure Camera
1. Select **Main Camera** in Hierarchy
2. Set **Projection** to **Orthographic**
3. Set **Size** to `5` (for mobile aspect ratio)
4. Set **Position** to `(0, 0, -10)`

### 2.2 Create UI Canvas
1. Right-click in Hierarchy → **UI → Canvas**
2. Set **Canvas Scaler** component:
   - **UI Scale Mode**: Scale With Screen Size
   - **Reference Resolution**: 1920 x 1080
   - **Screen Match Mode**: Match Width Or Height
   - **Match**: 0.5

## Step 3: Create GameObjects

### 3.1 Game Manager
1. Right-click in Hierarchy → **Create Empty**
2. Rename to **GameManager**
3. Add **GameManager.cs** script (drag from Scripts folder)
4. In Inspector, configure:
   - **Enemy Prefab**: (assign after creating enemy prefab)
   - **Spawn Point**: (assign after creating spawn point)
   - **Debug Mode**: ☑️ (checked for testing)

### 3.2 Grid Manager
1. Right-click in Hierarchy → **Create Empty**
2. Rename to **GridManager**
3. Add **GridManager.cs** script
4. In Inspector, configure:
   - **Tower Prefab**: (assign after creating tower prefab)
   - **Tower Cost**: `20`
   - **Grid Width**: `5`
   - **Grid Height**: `3`
   - **Cell Size**: `1`
   - **Grid Origin**: `(-2, -1, 0)`

### 3.3 Base
1. Right-click in Hierarchy → **Create Empty**
2. Rename to **Base**
3. Add **Base.cs** script
4. Set **Position** to `(4, 0, 0)`
5. Add **Sprite Renderer** component:
   - **Sprite**: Square (from Unity's built-in sprites)
   - **Color**: Green (RGB: 0, 255, 0)
   - **Size**: `(1, 1)`

### 3.4 Enemy Spawn Point
1. Right-click in Hierarchy → **Create Empty**
2. Rename to **EnemySpawn**
3. Set **Position** to `(-5, 0, 0)`

### 3.5 Debug Manager
1. Right-click in Hierarchy → **Create Empty**
2. Rename to **DebugManager**
3. Add **DebugManager.cs** script

## Step 4: Create Prefabs

### 4.1 Enemy Prefab
1. Right-click in Hierarchy → **Create Empty**
2. Rename to **Enemy**
3. Add **Enemy.cs** script
4. Set **Tag** to **Enemy**
5. Add **Sprite Renderer**:
   - **Sprite**: Square
   - **Color**: Red (RGB: 255, 0, 0)
   - **Size**: `(0.5, 0.5)`
6. Add **Box Collider 2D**:
   - **Size**: `(0.5, 0.5)`
7. Drag from Hierarchy to **Assets/Prefabs** folder to create prefab
8. Delete original from Hierarchy

### 4.2 Tower Prefab
1. Right-click in Hierarchy → **Create Empty**
2. Rename to **Tower**
3. Add **Tower.cs** script
4. Add **Sprite Renderer**:
   - **Sprite**: Circle (from Unity's built-in sprites)
   - **Color**: Blue (RGB: 0, 0, 255)
   - **Size**: `(0.8, 0.8)`
5. Add **Circle Collider 2D**:
   - **Radius**: `0.4`
6. In Inspector, configure:
   - **Bullet Prefab**: (assign after creating bullet prefab)
   - **Fire Rate**: `2`
   - **Damage**: `2`
   - **Range**: `3`
7. Drag to **Assets/Prefabs** folder to create prefab
8. Delete original from Hierarchy

### 4.3 Bullet Prefab
1. Right-click in Hierarchy → **Create Empty**
2. Rename to **Bullet**
3. Add **Bullet.cs** script
4. Set **Tag** to **Bullet**
5. Add **Sprite Renderer**:
   - **Sprite**: Circle
   - **Color**: White (RGB: 255, 255, 255)
   - **Size**: `(0.2, 0.2)`
6. Add **Circle Collider 2D**:
   - **Radius**: `0.1`
7. Drag to **Assets/Prefabs** folder to create prefab
8. Delete original from Hierarchy

## Step 5: Assign Prefabs to Scripts

### 5.1 Game Manager
1. Select **GameManager** GameObject
2. In Inspector, assign:
   - **Enemy Prefab**: Drag Enemy prefab from Assets/Prefabs
   - **Spawn Point**: Drag EnemySpawn GameObject
   - **Initial Currency**: `50`
   - **Currency Per Kill**: `10`

### 5.2 Grid Manager
1. Select **GridManager** GameObject
2. In Inspector, assign:
   - **Tower Prefab**: Drag Tower prefab from Assets/Prefabs

### 5.3 Tower Prefab
1. Open **Tower** prefab (double-click in Assets/Prefabs)
2. In Inspector, assign:
   - **Bullet Prefab**: Drag Bullet prefab from Assets/Prefabs

## Step 6: Create Waypoints

### 6.1 Create Waypoint System
1. Right-click in Hierarchy → **Create Empty**
2. Rename to **Waypoints**
3. Create 5 child GameObjects:
   - **Waypoint0**: Position `(-5, 0, 0)` (same as spawn point)
   - **Waypoint1**: Position `(-2.5, 0, 0)`
   - **Waypoint2**: Position `(0, 0, 0)`
   - **Waypoint3**: Position `(2.5, 0, 0)`
   - **Waypoint4**: Position `(4, 0, 0)` (same as base position)

4. Select **GameManager**
5. In Inspector, set **Waypoints** array size to `5`
6. Drag each Waypoint GameObject to corresponding array element

## Step 7: Create UI Elements

### 7.1 Currency Display
1. Right-click on Canvas → **UI → Text**
2. Rename to **CurrencyText**
3. Set **Text** to: "Coins: 50"
4. Set **Font Size**: 36
5. Set **Color**: Yellow
6. Set **Position**: Top-left corner

### 7.2 Base Health Bar
1. Right-click on Canvas → **UI → Slider**
2. Rename to **HealthBar**
3. Delete **Handle Slide Area** child
4. Set **Min Value**: 0
5. Set **Max Value**: 10
6. Set **Value**: 10
7. Set **Position**: Top-center

### 7.3 Wave Counter
1. Right-click on Canvas → **UI → Text**
2. Rename to **WaveText**
3. Set **Text** to: "Wave: 1/5"
4. Set **Font Size**: 36
5. Set **Color**: White
6. Set **Position**: Top-right corner

### 7.4 Debug Panel
1. Right-click on Canvas → **UI → Panel**
2. Rename to **DebugPanel**
3. Set **Color**: Semi-transparent black
4. Add **Vertical Layout Group** component
5. Add **Content Size Fitter**:
   - **Vertical Fit**: Preferred Size

6. Add 4 buttons as children:
   - **Skip Wave Button**: Text = "Skip Wave"
   - **Add Coins Button**: Text = "+50 Coins"
   - **Instant Win Button**: Text = "Win Game"
   - **Add Health Button**: Text = "+5 Health"

7. Select **DebugManager**
8. In Inspector, assign:
   - **Skip Wave Button**: Drag Skip Wave Button
   - **Add Coins Button**: Drag Add Coins Button
   - **Instant Win Button**: Drag Instant Win Button
   - **Add Health Button**: Drag Add Health Button
   - **Debug Panel**: Drag DebugPanel GameObject

## Step 8: Final Configuration

### 8.1 Layer Setup
1. Go to **Edit → Project Settings → Tags and Layers**
2. Under **Layers**, ensure:
   - **Default** layer is used for all objects
   - No special layers needed for MVP

### 8.2 Physics Settings
1. Go to **Edit → Project Settings → Physics 2D**
2. Ensure **Gravity** is set to `(0, 0)` for 2D top-down game

## Testing Checklist

After setup, test the following:
- [ ] Game starts with 50 coins
- [ ] Can place towers by clicking/tapping on grid
- [ ] Enemies spawn from left side
- [ ] Towers automatically shoot at enemies
- [ ] Enemies take damage and die
- [ ] Base health decreases when enemies reach it
- [ ] Debug buttons work correctly
- [ ] Game ends after 5 waves or when base health reaches 0

## Troubleshooting

### Common Issues:
1. **Enemies not spawning**: Check GameManager's enemyPrefab and spawnPoint assignments
2. **Towers not shooting**: Ensure Tower prefab has bulletPrefab assigned
3. **Touch input not working**: Check GridManager's camera reference
4. **UI not updating**: Ensure GameManager has references to UI Text elements

## Next Steps
After completing this setup, the MVP will be ready for testing and iteration based on the GDD requirements.
