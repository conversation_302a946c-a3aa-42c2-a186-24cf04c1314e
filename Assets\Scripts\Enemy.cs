using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Enemy : MonoBehaviour
{
    [Header("Enemy Settings")]
    public float health = 5f;
    public int damageToBase = 1;
    
    [Header("Visual")]
    public Color enemyColor = Color.red;
    
    private float currentHealth;
    private float moveSpeed = 2f;
    private int currentWaypointIndex = 0;
    private List<Transform> waypoints;
    private GameManager gameManager;
    
    private SpriteRenderer spriteRenderer;
    
    void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
        if (spriteRenderer == null)
        {
            spriteRenderer = gameObject.AddComponent<SpriteRenderer>();
        }
        
        // Create a simple square sprite
        CreateSquareSprite();
    }
    
    void Start()
    {
        currentHealth = health;
        UpdateColor();
    }
    
    void Update()
    {
        MoveTowardsWaypoint();
    }
    
    public void SetSpeed(float speed)
    {
        moveSpeed = speed;
    }
    
    public void SetWaypoints(List<Transform> waypointList)
    {
        waypoints = waypointList;
    }
    
    public void SetWaveManager(GameManager manager)
    {
        gameManager = manager;
        if (gameManager != null && gameManager.waypoints.Count > 0)
        {
            waypoints = gameManager.waypoints;
        }
    }
    
    private void MoveTowardsWaypoint()
    {
        if (waypoints == null || waypoints.Count == 0 || currentWaypointIndex >= waypoints.Count)
        {
            ReachBase();
            return;
        }
        
        Transform targetWaypoint = waypoints[currentWaypointIndex];
        Vector3 direction = (targetWaypoint.position - transform.position).normalized;
        transform.position += direction * moveSpeed * Time.deltaTime;
        
        if (Vector3.Distance(transform.position, targetWaypoint.position) < 0.1f)
        {
            currentWaypointIndex++;
        }
    }
    
    public void TakeDamage(float damage)
    {
        currentHealth -= damage;
        
        if (currentHealth <= 0)
        {
            Die();
        }
        else
        {
            // Flash effect
            StartCoroutine(DamageFlash());
        }
    }
    
    private IEnumerator DamageFlash()
    {
        Color originalColor = spriteRenderer.color;
        spriteRenderer.color = Color.white;
        yield return new WaitForSeconds(0.1f);
        spriteRenderer.color = originalColor;
    }
    
    private void Die()
    {
        if (gameManager != null)
        {
            gameManager.EnemyDefeated();
        }
        
        Destroy(gameObject);
    }
    
    private void ReachBase()
    {
        if (gameManager != null)
        {
            gameManager.DamageBase(damageToBase);
        }
        
        Destroy(gameObject);
    }
    
    private void CreateSquareSprite()
    {
        // Create a simple 1x1 white texture
        Texture2D texture = new Texture2D(1, 1);
        texture.SetPixel(0, 0, Color.white);
        texture.Apply();
        
        // Create sprite from texture
        Sprite sprite = Sprite.Create(texture, new Rect(0, 0, 1, 1), new Vector2(0.5f, 0.5f), 1f);
        spriteRenderer.sprite = sprite;
        spriteRenderer.color = enemyColor;
        
        // Set size
        transform.localScale = new Vector3(0.5f, 0.5f, 1f);
    }
    
    private void UpdateColor()
    {
        if (spriteRenderer != null)
        {
            spriteRenderer.color = enemyColor;
        }
    }
}
