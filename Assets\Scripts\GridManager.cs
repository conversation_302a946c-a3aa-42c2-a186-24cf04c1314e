using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GridManager : MonoBehaviour
{
    [Header("Grid Settings")]
    public int gridWidth = 5;
    public int gridHeight = 3;
    public float cellSize = 1f;
    public Vector3 gridOrigin = new Vector3(-2f, -1f, 0f);
    
    [Header("Tower Settings")]
    public GameObject towerPrefab;
    public int towerCost = 20;
    
    [Header("Visual")]
    public bool showGridInEditor = true;
    public Color gridColor = Color.gray;
    
    private bool[,] gridOccupied;
    private Camera mainCamera;
    
    void Start()
    {
        gridOccupied = new bool[gridWidth, gridHeight];
        mainCamera = Camera.main;
        
        // Create grid cells
        CreateGridCells();
    }
    
    void Update()
    {
        HandleTouchInput();
    }
    
    void HandleTouchInput()
    {
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);
            
            if (touch.phase == TouchPhase.Began)
            {
                Vector3 touchPosition = mainCamera.ScreenToWorldPoint(touch.position);
                touchPosition.z = 0;
                
                TryPlaceTower(touchPosition);
            }
        }
        
        // Also handle mouse input for testing in editor
#if UNITY_EDITOR
        if (Input.GetMouseButtonDown(0))
        {
            Vector3 mousePosition = mainCamera.ScreenToWorldPoint(Input.mousePosition);
            mousePosition.z = 0;
            
            TryPlaceTower(mousePosition);
        }
#endif
    }
    
    void TryPlaceTower(Vector3 worldPosition)
    {
        Vector2Int gridPos = WorldToGridPosition(worldPosition);
        
        if (IsValidGridPosition(gridPos) && !IsCellOccupied(gridPos))
        {
            if (GameManager.Instance != null && GameManager.Instance.SpendCurrency(towerCost))
            {
                PlaceTower(gridPos);
            }
        }
    }
    
    Vector2Int WorldToGridPosition(Vector3 worldPosition)
    {
        Vector3 localPosition = worldPosition - gridOrigin;
        int x = Mathf.FloorToInt(localPosition.x / cellSize);
        int y = Mathf.FloorToInt(localPosition.y / cellSize);
        
        return new Vector2Int(x, y);
    }
    
    Vector3 GridToWorldPosition(int x, int y)
    {
        return gridOrigin + new Vector3(x * cellSize + cellSize / 2f, y * cellSize + cellSize / 2f, 0f);
    }
    
    bool IsValidGridPosition(Vector2Int gridPos)
    {
        return gridPos.x >= 0 && gridPos.x < gridWidth && 
               gridPos.y >= 0 && gridPos.y < gridHeight;
    }
    
    bool IsCellOccupied(Vector2Int gridPos)
    {
        return gridOccupied[gridPos.x, gridPos.y];
    }
    
    void PlaceTower(Vector2Int gridPos)
    {
        Vector3 worldPosition = GridToWorldPosition(gridPos.x, gridPos.y);
        GameObject tower = Instantiate(towerPrefab, worldPosition, Quaternion.identity);
        
        gridOccupied[gridPos.x, gridPos.y] = true;
    }
    
    void CreateGridCells()
    {
        // Create empty GameObjects for each grid cell (for visualization)
        for (int x = 0; x < gridWidth; x++)
        {
            for (int y = 0; y < gridHeight; y++)
            {
                Vector3 cellPosition = GridToWorldPosition(x, y);
                
                // Create a visual representation of the grid cell
                GameObject cell = new GameObject($"GridCell_{x}_{y}");
                cell.transform.position = cellPosition;
                cell.transform.parent = transform;
                
                // Add a sprite renderer for visualization
                SpriteRenderer renderer = cell.AddComponent<SpriteRenderer>();
                CreateGridCellSprite(renderer);
            }
        }
    }
    
    void CreateGridCellSprite(SpriteRenderer renderer)
    {
        // Create a simple square outline texture
        int textureSize = 64;
        Texture2D texture = new Texture2D(textureSize, textureSize);
        
        Color[] colors = new Color[textureSize * textureSize];
        
        for (int y = 0; y < textureSize; y++)
        {
            for (int x = 0; x < textureSize; x++)
            {
                int index = y * textureSize + x;
                
                // Create outline
                if (x < 2 || x >= textureSize - 2 || y < 2 || y >= textureSize - 2)
                {
                    colors[index] = Color.white;
                }
                else
                {
                    colors[index] = Color.clear;
                }
            }
        }
        
        texture.SetPixels(colors);
        texture.Apply();
        
        // Create sprite from texture
        Sprite sprite = Sprite.Create(texture, new Rect(0, 0, textureSize, textureSize), new Vector2(0.5f, 0.5f), textureSize);
        renderer.sprite = sprite;
        renderer.color = gridColor;
        
        // Set size
        renderer.transform.localScale = new Vector3(cellSize, cellSize, 1f);
    }
    
    void OnDrawGizmos()
    {
        if (!showGridInEditor)
            return;
        
        Gizmos.color = gridColor;
        
        for (int x = 0; x <= gridWidth; x++)
        {
            Vector3 start = gridOrigin + new Vector3(x * cellSize, 0, 0);
            Vector3 end = gridOrigin + new Vector3(x * cellSize, gridHeight * cellSize, 0);
            Gizmos.DrawLine(start, end);
        }
        
        for (int y = 0; y <= gridHeight; y++)
        {
            Vector3 start = gridOrigin + new Vector3(0, y * cellSize, 0);
            Vector3 end = gridOrigin + new Vector3(gridWidth * cellSize, y * cellSize, 0);
            Gizmos.DrawLine(start, end);
        }
    }
}
