using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Tower : MonoBehaviour
{
    [Header("Tower Settings")]
    public float damage = 2f;
    public float range = 3f;
    public float fireRate = 0.5f; // Every 2 seconds
    
    [Header("Visual")]
    public Color towerColor = Color.blue;
    
    [Header("References")]
    public GameObject bulletPrefab;
    public Transform firePoint;
    
    private float fireCountdown = 0f;
    private Transform target;
    private SpriteRenderer spriteRenderer;
    
    void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
        if (spriteRenderer == null)
        {
            spriteRenderer = gameObject.AddComponent<SpriteRenderer>();
        }
        
        // Create a simple circle sprite
        CreateCircleSprite();
        
        if (firePoint == null)
        {
            firePoint = transform;
        }
    }
    
    void Start()
    {
        InvokeRepeating("UpdateTarget", 0f, 0.1f);
    }
    
    void Update()
    {
        if (target == null)
            return;
        
        if (fireCountdown <= 0f)
        {
            Shoot();
            fireCountdown = 1f / fireRate;
        }
        
        fireCountdown -= Time.deltaTime;
    }
    
    void UpdateTarget()
    {
        GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
        float shortestDistance = Mathf.Infinity;
        GameObject nearestEnemy = null;
        
        foreach (GameObject enemy in enemies)
        {
            float distanceToEnemy = Vector3.Distance(transform.position, enemy.transform.position);
            if (distanceToEnemy < shortestDistance)
            {
                shortestDistance = distanceToEnemy;
                nearestEnemy = enemy;
            }
        }
        
        if (nearestEnemy != null && shortestDistance <= range)
        {
            target = nearestEnemy.transform;
        }
        else
        {
            target = null;
        }
    }
    
    void Shoot()
    {
        if (bulletPrefab != null && target != null)
        {
            GameObject bulletGO = Instantiate(bulletPrefab, firePoint.position, Quaternion.identity);
            Bullet bullet = bulletGO.GetComponent<Bullet>();
            
            if (bullet != null)
            {
                bullet.Seek(target);
                bullet.SetDamage(damage);
            }
        }
    }
    
    void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, range);
    }
    
    private void CreateCircleSprite()
    {
        // Create a simple circle texture
        int textureSize = 64;
        Texture2D texture = new Texture2D(textureSize, textureSize);
        
        Color[] colors = new Color[textureSize * textureSize];
        Vector2 center = new Vector2(textureSize / 2f, textureSize / 2f);
        float radius = textureSize / 2f - 2f;
        
        for (int y = 0; y < textureSize; y++)
        {
            for (int x = 0; x < textureSize; x++)
            {
                int index = y * textureSize + x;
                Vector2 pos = new Vector2(x, y);
                float distance = Vector2.Distance(pos, center);
                
                if (distance <= radius)
                {
                    colors[index] = Color.white;
                }
                else
                {
                    colors[index] = Color.clear;
                }
            }
        }
        
        texture.SetPixels(colors);
        texture.Apply();
        
        // Create sprite from texture
        Sprite sprite = Sprite.Create(texture, new Rect(0, 0, textureSize, textureSize), new Vector2(0.5f, 0.5f), textureSize);
        spriteRenderer.sprite = sprite;
        spriteRenderer.color = towerColor;
        
        // Set size
        transform.localScale = new Vector3(0.5f, 0.5f, 1f);
    }
}
