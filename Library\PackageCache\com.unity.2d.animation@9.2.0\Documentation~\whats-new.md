# What's new in version 9.0

## Added
- Added a new **Sprite Library Editor** to make authoring and editing of Sprite Libraries easier.

## Updated
- 2D Animation now depends on the [Collections package](https://docs.unity3d.com/Packages/com.unity.collections@latest), which enables bursted and multithreaded Sprite deformation by default. 
- Sprite Skin's [Auto Rebind](SpriteSkin.md#auto-rebind) can now swap between all bones underneath the rootBone.