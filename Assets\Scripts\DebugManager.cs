using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class DebugManager : MonoBehaviour
{
    [Header("Debug UI")]
    public Button skipWaveButton;
    public Button addCoinsButton;
    public Button instantWinButton;
    public Button addHealthButton;
    public Button damageBaseButton;
    public GameObject debugPanel;
    
    private GameManager gameManager;
    
    void Start()
    {
        gameManager = FindObjectOfType<GameManager>();
        
        if (gameManager == null)
        {
            Debug.LogError("GameManager not found! Debug buttons will not work.");
            return;
        }
        
        SetupDebugButtons();
        
        // Hide debug panel by default
        if (debugPanel != null && !gameManager.debugMode)
        {
            debugPanel.SetActive(false);
        }
    }
    
    void SetupDebugButtons()
    {
        if (skipWaveButton != null)
            skipWaveButton.onClick.AddListener(OnSkipWaveClicked);
        
        if (addCoinsButton != null)
            addCoinsButton.onClick.AddListener(OnAddCoinsClicked);
        
        if (instantWinButton != null)
            instantWinButton.onClick.AddListener(OnInstantWinClicked);
        
        if (addHealthButton != null)
            addHealthButton.onClick.AddListener(OnAddHealthClicked);

        if (damageBaseButton != null)
            damageBaseButton.onClick.AddListener(OnDamageBaseClicked);
    }
    
    void OnSkipWaveClicked()
    {
        if (gameManager != null)
        {
            gameManager.Debug_SkipWave();
            Debug.Log("Debug: Skipped to next wave");
        }
    }
    
    void OnAddCoinsClicked()
    {
        if (gameManager != null)
        {
            gameManager.Debug_AddCoins();
            Debug.Log("Debug: Added 50 coins");
        }
    }
    
    void OnInstantWinClicked()
    {
        if (gameManager != null)
        {
            gameManager.Debug_InstantWin();
            Debug.Log("Debug: Instant win triggered");
        }
    }
    
    void OnAddHealthClicked()
    {
        if (gameManager != null)
        {
            gameManager.Debug_AddHealth();
            Debug.Log("Debug: Added health to base");
        }
    }

    void OnDamageBaseClicked()
    {
        if (gameManager != null)
        {
            gameManager.Debug_DamageBase();
            Debug.Log("Debug: Damaged base");
        }
    }
    
    // Toggle debug panel visibility
    public void ToggleDebugPanel()
    {
        if (debugPanel != null)
        {
            debugPanel.SetActive(!debugPanel.activeSelf);
        }
    }
}
